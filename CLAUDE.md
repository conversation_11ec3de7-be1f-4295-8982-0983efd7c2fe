# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Full Stack FastAPI Template** with questionnaire management capabilities. The project includes:

- **Backend**: FastAPI with SQLModel ORM, PostgreSQL, JWT authentication
- **Frontend**: React + TypeScript with Chakra UI, TanStack Router/Query
- **Questionnaire System**: Client management, survey types, and results tracking
- **Deployment**: Docker Compose with Traefik proxy

## Architecture

### Backend (Python)
- **Framework**: FastAPI with Pydantic validation
- **ORM**: SQLModel (Pydantic + SQLAlchemy)
- **Database**: PostgreSQL with Alembic migrations
- **Authentication**: JWT tokens with password hashing
- **API Structure**: RESTful endpoints under `/api/v1/`

### Frontend (React)
- **Framework**: React 18 with TypeScript
- **Router**: TanStack Router (file-based routing)
- **State**: TanStack Query for server state management
- **UI**: Chakra UI component library
- **Build**: Vite for development and production

### Key Features
- User management system
- Client management
- Questionnaire/survey system
- Blog/content management
- File upload capabilities
- Email notifications
- Admin dashboard

## Development Setup

### Quick Start
```bash
# Start entire stack with Docker
docker compose watch

# Access URLs:
# Frontend: http://localhost:5173
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
# Database Admin: http://localhost:8080
```

### Backend Development
```bash
# Install dependencies
cd backend
uv sync
source .venv/bin/activate

# Run locally (requires PostgreSQL)
fastapi dev app/main.py

# Run tests
bash scripts/test.sh

# Database migrations
alembic revision --autogenerate -m "description"
alembic upgrade head
```

### Frontend Development
```bash
cd frontend
npm install
npm run dev

# Generate API client from OpenAPI
npm run generate-client

# Run E2E tests
npx playwright test
```

## Build Commands

### Backend
```bash
# Install dependencies
uv sync

# Run tests
pytest
pytest -x  # Stop on first failure

# Linting
ruff check .
ruff format .

# Type checking
mypy .

# Build Docker image
docker build -t backend ./backend
```

### Frontend
```bash
# Development
npm run dev

# Build for production
npm run build

# Lint and fix
npm run lint

# Type checking
npm run build  # Includes TypeScript check

# Build Docker image
docker build -t frontend ./frontend
```

## Key Project Files

### Backend Structure
```
backend/
├── app/
│   ├── api/routes/       # API endpoints
│   │   ├── questionnaires.py    # Questionnaire system
│   │   ├── clients.py           # Client management
│   │   ├── users.py            # User management
│   │   └── ...
│   ├── models.py         # SQLModel database models
│   ├── crud.py          # Database operations
│   ├── core/            # Core config, security, db
│   └── tests/           # pytest test suite
├── alembic/            # Database migrations
└── scripts/            # Build and test scripts
```

### Frontend Structure
```
frontend/
├── src/
│   ├── routes/          # TanStack Router pages
│   │   ├── _layout/questionnaire-dashboard.tsx
│   │   ├── _layout/client.$clientId.tsx
│   │   └── ...
│   ├── components/      # React components
│   │   ├── Questionnaires/
│   │   ├── Clients/
│   │   └── ...
│   ├── client/          # Generated API client
│   └── hooks/           # Custom React hooks
├── tests/              # Playwright E2E tests
└── vite.config.ts      # Vite configuration
```

## Questionnaire System

### Core Models
- **Client**: Individual or organization taking surveys
- **Questionnaire**: Survey template with questions
- **SurveyType**: Different types of surveys (e.g., happiness, stress)
- **Question**: Individual questions within questionnaires
- **Result**: Client's responses and scores

### Key Endpoints
- `GET /api/v1/questionnaires/` - List questionnaires
- `POST /api/v1/questionnaires/` - Create questionnaire
- `GET /api/v1/clients/{id}/questionnaires` - Client questionnaires
- `POST /api/v1/questionnaires/{id}/results` - Submit responses

## Database Operations

### Migrations
```bash
# Create new migration
alembic revision --autogenerate -m "add new feature"

# Apply migrations
alembic upgrade head

# Downgrade migration
alembic downgrade -1
```

### Database Access
```python
# In backend code
from app.core.db import SessionDep
from app.models import Client, Questionnaire

# Using dependency injection
async def get_clients(session: SessionDep):
    return session.exec(select(Client)).all()
```

## Testing

### Backend Tests
```bash
# Run all tests
bash scripts/test.sh

# Run specific test file
pytest tests/api/routes/test_questionnaires.py

# Run with coverage
pytest --cov=app
```

### Frontend Tests
```bash
# Run E2E tests
npx playwright test

# Run specific test
npx playwright test login.spec.ts

# Run in UI mode
npx playwright test --ui
```

## API Client Generation

### Automatic Generation
```bash
# From project root
./scripts/generate-client.sh
```

### Manual Generation
```bash
# 1. Start backend
docker compose up backend

# 2. Generate client
cd frontend
npm run generate-client
```

## Environment Configuration

### Required Environment Variables
Create `.env` file with:
```bash
# Database
POSTGRES_PASSWORD=your_password
POSTGRES_USER=your_user
POSTGRES_DB=your_db

# Security
SECRET_KEY=your_secret_key
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=your_password

# URLs
DOMAIN=localhost
VITE_API_URL=http://localhost:8000
```

## Task Master Integration (Existing)

This project includes Task Master AI for task management. See the original Task Master section below for full integration details.

## Common Development Tasks

### Adding New API Endpoint
1. Create route in `backend/app/api/routes/`
2. Add model in `backend/app/models.py`
3. Create migration: `alembic revision --autogenerate`
4. Generate frontend client: `npm run generate-client`
5. Create frontend components

### Adding New Frontend Page
1. Create file in `frontend/src/routes/_layout/`
2. Add route configuration
3. Create corresponding components
4. Add API integration using generated client

### Database Schema Changes
1. Update models in `backend/app/models.py`
2. Generate migration: `alembic revision --autogenerate -m "description"`
3. Apply migration: `alembic upgrade head`
4. Regenerate API client: `npm run generate-client`

## Deployment

### Production Build
```bash
# Build and start production stack
docker compose -f docker-compose.yml up --build

# Or use deployment scripts
./scripts/deploy.sh
```

### Environment for Production
```bash
# Set production domain
DOMAIN=yourdomain.com

# Configure SSL certificates (handled by Traefik)
# Ensure all secrets are properly set
```

---

# Task Master AI - Claude Code Integration Guide

## Essential Commands

### Core Workflow Commands

```bash
# Project Setup
task-master init                                    # Initialize Task Master in current project
task-master parse-prd .taskmaster/docs/prd.txt      # Generate tasks from PRD document
task-master models --setup                        # Configure AI models interactively

# Daily Development Workflow
task-master list                                   # Show all tasks with status
task-master next                                   # Get next available task to work on
task-master show <id>                             # View detailed task information (e.g., task-master show 1.2)
task-master set-status --id=<id> --status=done    # Mark task complete

# Task Management
task-master add-task --prompt="description" --research        # Add new task with AI assistance
task-master expand --id=<id> --research --force              # Break task into subtasks
task-master update-task --id=<id> --prompt="changes"         # Update specific task
task-master update --from=<id> --prompt="changes"            # Update multiple tasks from ID onwards
task-master update-subtask --id=<id> --prompt="notes"        # Add implementation notes to subtask

# Analysis & Planning
task-master analyze-complexity --research          # Analyze task complexity
task-master complexity-report                      # View complexity analysis
task-master expand --all --research               # Expand all eligible tasks

# Dependencies & Organization
task-master add-dependency --id=<id> --depends-on=<id>       # Add task dependency
task-master move --from=<id> --to=<id>                       # Reorganize task hierarchy
task-master validate-dependencies                            # Check for dependency issues
task-master generate                                         # Update task markdown files (usually auto-called)
```

## Key Files & Project Structure

### Core Files

- `.taskmaster/tasks/tasks.json` - Main task data file (auto-managed)
- `.taskmaster/config.json` - AI model configuration (use `task-master models` to modify)
- `.taskmaster/docs/prd.txt` - Product Requirements Document for parsing
- `.taskmaster/tasks/*.txt` - Individual task files (auto-generated from tasks.json)
- `.env` - API keys for CLI usage

### Claude Code Integration Files

- `CLAUDE.md` - Auto-loaded context for Claude Code (this file)
- `.claude/settings.json` - Claude Code tool allowlist and preferences
- `.claude/commands/` - Custom slash commands for repeated workflows
- `.mcp.json` - MCP server configuration (project-specific)

### Directory Structure

```
project/
├── .taskmaster/
│   ├── tasks/              # Task files directory
│   │   ├── tasks.json      # Main task database
│   │   ├── task-1.md      # Individual task files
│   │   └── task-2.md
│   ├── docs/              # Documentation directory
│   │   ├── prd.txt        # Product requirements
│   ├── reports/           # Analysis reports directory
│   │   └── task-complexity-report.json
│   ├── templates/         # Template files
│   │   └── example_prd.txt  # Example PRD template
│   └── config.json        # AI models & settings
├── .claude/
│   ├── settings.json      # Claude Code configuration
│   └── commands/         # Custom slash commands
├── .env                  # API keys
├── .mcp.json            # MCP configuration
└── CLAUDE.md            # This file - auto-loaded by Claude Code
```

## MCP Integration

Task Master provides an MCP server that Claude Code can connect to. Configure in `.mcp.json`:

```json
{
  "mcpServers": {
    "task-master-ai": {
      "command": "npx",
      "args": ["-y", "--package=task-master-ai", "task-master-ai"],
      "env": {
        "ANTHROPIC_API_KEY": "your_key_here",
        "PERPLEXITY_API_KEY": "your_key_here",
        "OPENAI_API_KEY": "OPENAI_API_KEY_HERE",
        "GOOGLE_API_KEY": "GOOGLE_API_KEY_HERE",
        "XAI_API_KEY": "XAI_API_KEY_HERE",
        "OPENROUTER_API_KEY": "OPENROUTER_API_KEY_HERE",
        "MISTRAL_API_KEY": "MISTRAL_API_KEY_HERE",
        "AZURE_OPENAI_API_KEY": "AZURE_OPENAI_API_KEY_HERE",
        "OLLAMA_API_KEY": "OLLAMA_API_KEY_HERE"
      }
    }
  }
}
```

### Essential MCP Tools

```javascript
help; // = shows available taskmaster commands
// Project setup
initialize_project; // = task-master init
parse_prd; // = task-master parse-prd

// Daily workflow
get_tasks; // = task-master list
next_task; // = task-master next
get_task; // = task-master show <id>
set_task_status; // = task-master set-status

// Task management
add_task; // = task-master add-task
expand_task; // = task-master expand
update_task; // = task-master update-task
update_subtask; // = task-master update-subtask
update; // = task-master update

// Analysis
analyze_project_complexity; // = task-master analyze-complexity
complexity_report; // = task-master complexity-report
```