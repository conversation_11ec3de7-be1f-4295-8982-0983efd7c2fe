# Clients Management Dashboard Feature

## Overview
Create a comprehensive clients management page for the dashboard frontend that allows administrators to view, manage, and interact with client data.

## Requirements

### Core Features
1. **Clients List Page**
   - Display clients in a table format
   - Show columns: Created At, Name (Full Name), Email, Phone, Status (Active/Inactive)
   - Implement pagination (20 items per page)
   - Sort functionality by Created At (ascending/descending)
   - Filter functionality by Status (Active, Inactive, All)

2. **Add Client Dialog**
   - Form fields: Email, Full Name, Phone Number, Password, Confirm Password
   - Validation for all fields
   - Email uniqueness validation
   - Password confirmation matching
   - Set client as active by default

3. **Edit Client Dialog**
   - Form fields: Email, Full Name, Phone Number, Status (Active/Inactive)
   - Pre-populate with existing client data
   - Email uniqueness validation (excluding current client)
   - Update client information

### Technical Requirements
1. **Backend API Extensions**
   - Create admin endpoints for client management
   - GET /api/v1/admin/clients - List clients with pagination, sorting, filtering
   - POST /api/v1/admin/clients - Create new client
   - PUT /api/v1/admin/clients/{id} - Update client
   - DELETE /api/v1/admin/clients/{id} - Delete client (optional)

2. **Frontend Implementation**
   - Follow existing code patterns and component structure
   - Use Chakra UI components consistent with existing pages
   - Implement proper TypeScript types
   - Use React Hook Form for form management
   - Use TanStack Query for data fetching and caching
   - Follow existing routing patterns

3. **Component Structure**
   - Create `/frontend/src/components/Clients/` folder
   - AddClient.tsx - Add client dialog component
   - EditClient.tsx - Edit client dialog component
   - ClientActionsMenu.tsx - Actions menu for each client row
   - Create `/frontend/src/routes/_layout/clients.tsx` - Main clients page

### UI/UX Requirements
1. **Table Design**
   - Responsive design (mobile and desktop)
   - Loading states with skeleton components
   - Empty state when no clients found
   - Proper error handling and display

2. **Dialogs**
   - Modal dialogs for add/edit operations
   - Form validation with error messages
   - Loading states during form submission
   - Success/error toast notifications

3. **Navigation**
   - Add "Clients" menu item to sidebar
   - Only visible to admin users (is_superuser)
   - Use appropriate icon (FiUsers)

### Data Requirements
1. **Client Model Extensions**
   - Ensure created_at field is available and properly formatted
   - Status field should be boolean (is_active)
   - All fields should be properly typed in frontend

2. **Sorting and Filtering**
   - Sort by created_at (newest first by default)
   - Filter by is_active status
   - Maintain filter/sort state in URL parameters

### Security Requirements
1. **Admin Only Access**
   - Restrict access to admin users only
   - Proper authentication and authorization checks
   - Secure API endpoints with admin permissions

2. **Data Validation**
   - Server-side validation for all inputs
   - Client-side validation for better UX
   - Proper error handling and user feedback

## Success Criteria
1. Admin users can view all clients in a paginated table
2. Admin users can sort clients by creation date
3. Admin users can filter clients by active status
4. Admin users can add new clients through a dialog
5. Admin users can edit existing client information
6. All operations provide proper feedback to users
7. The interface is responsive and follows existing design patterns
8. Code follows existing patterns and is properly typed
