import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"
import React from "react"
import {
    But<PERSON>,
    DialogActionTrigger,
    DialogTitle,
    Input,
    Text,
    VStack,
    NumberInput,
    FileUpload,
    Box,
    Icon,
    Textarea,
    Flex,
} from "@chakra-ui/react"
import { useState, useEffect } from "react"
import { FaPlus, FaCloudUploadAlt } from "react-icons/fa"
import { type Body_blogs_create_blog, BlogsService } from "../../client"
import type { ApiError } from "../../client/core/ApiError"
import useCustomToast from "../../hooks/useCustomToast"
import { handleError } from "../../utils"
import {
    DialogBody,
    DialogCloseTrigger,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogRoot,
    DialogTrigger,
} from "../ui/dialog"
import { Field } from "../ui/field"


const AddActivity = () => {
    const [isOpen, setIsOpen] = useState(false)
    const queryClient = useQueryClient()
    const { showSuccessToast, showErrorToast } = useCustomToast()
    const {
        register,
        handleSubmit,
        reset,
        setValue,
        formState: { errors, isValid, isSubmitting },
    } = useForm<Body_blogs_create_blog>({
        mode: "onBlur",
        criteriaMode: "all",
        defaultValues: {
            title: "",
            description: "",
            image1: undefined,
            image2: undefined,
            image3: undefined,
        },
    })

    // Watch for file changes

    const [previewUrls, setPreviewUrls] = useState<Record<string, string | null>>({
        image1: null,
        image2: null,
        image3: null
    })

    // Handle file changes for each image field
    const handleFileChange = (
        event: React.ChangeEvent<HTMLInputElement>,
        fieldName: "image1" | "image2" | "image3"
    ) => {
        const file = event.target.files?.[0]
        if (file) {
            setValue(fieldName, file, { shouldValidate: true })
            const url = URL.createObjectURL(file)
            setPreviewUrls(prev => ({ ...prev, [fieldName]: url }))
        }
    }

    useEffect(() => {
        return () => {
            // Cleanup all preview URLs
            Object.values(previewUrls).forEach(url => {
                if (url) URL.revokeObjectURL(url)
            })
        }
    }, [previewUrls])
    const mutation = useMutation({
        mutationFn: (data: Body_blogs_create_blog) => {
            // Create clean formData object with only defined files
            const formData: Body_blogs_create_blog = {
                title: data.title,
                description: data.description || '',
            };
            
            // Only include image fields that have actual File objects
            if (data.image1 instanceof File) formData.image1 = data.image1;
            if (data.image2 instanceof File) formData.image2 = data.image2;
            if (data.image3 instanceof File) formData.image3 = data.image3;

            return BlogsService.blogsCreateBlog({ formData });
        },
        onSuccess: () => {
            showSuccessToast("Item created successfully.")
            reset()
            // Clear all preview URLs
            Object.keys(previewUrls).forEach(key => {
                if (previewUrls[key]) {
                    URL.revokeObjectURL(previewUrls[key]!);
                }
            });
            setPreviewUrls({
                image1: null,
                image2: null,
                image3: null
            });

            setIsOpen(false)
        },
        onError: (err: ApiError) => {
            handleError(err)
        },
        onSettled: () => {
            queryClient.invalidateQueries({ queryKey: ["activity"] })
        },
    })

    const onSubmit: SubmitHandler<Body_blogs_create_blog> = (data) => {

        mutation.mutate(data)
    }

    return (
        <DialogRoot
            size={{ base: "xs", md: "md" }}
            placement="center"
            open={isOpen}
            onOpenChange={({ open }) => {
                setIsOpen(open);
                // If dialog is closing, reset the form
                if (!open) {
                    reset();
                    // If you're using preview URL state, reset that too
                    // Clear all preview URLs
                    Object.keys(previewUrls).forEach(key => {
                        if (previewUrls[key]) {
                            URL.revokeObjectURL(previewUrls[key]!);
                        }
                    });
                    setPreviewUrls({
                        image1: null,
                        image2: null,
                        image3: null
                    });
                }
            }}
        >
            <DialogTrigger asChild>
                <Button value="add-item" my={4}>
                    <FaPlus fontSize="16px" />
                    Add Activity
                </Button>
            </DialogTrigger>
            <DialogContent>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogHeader>
                        <DialogTitle>Add Activity</DialogTitle>
                    </DialogHeader>
                    <DialogBody>
                        <Text mb={4}>Fill in the details to add a new Activity.</Text>
                        <VStack gap={4}>
                            <Field
                                required
                                invalid={!!errors.title}
                                errorText={errors.title?.message}
                                label="Title"
                            >
                                <Input
                                    id="title"
                                    {...register("title", {
                                        required: "Title is required.",
                                    })}
                                    placeholder="Title"
                                    type="text"
                                />
                            </Field>

                            <Field
                                required
                                invalid={!!errors.description}
                                errorText={errors.description?.message}
                                label="Description"
                            >
                                <Textarea
                                    h="200px"
                                    id="description"
                                    {...register("description")}
                                    placeholder="Description"
                                />

                            </Field>

                            {(["image1", "image2", "image3"] as const).map((fieldName) => (
                                <Field
                                    key={fieldName}
                                    invalid={!!errors[fieldName]}
                                    errorText={errors[fieldName]?.message}
                                    label={`Image ${fieldName.charAt(5)}`}
                                >
                                    <FileUpload.Root accept={["image/png", "image/jpeg"]}>
                                        <FileUpload.HiddenInput
                                            {...register(fieldName)}
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => handleFileChange(e, fieldName)}
                                        />
                                        <FileUpload.Trigger asChild>
                                            <Button variant="outline" size="sm">
                                                <FaCloudUploadAlt /> Upload {fieldName}
                                            </Button>
                                        </FileUpload.Trigger>
                                        <FileUpload.List />
                                    </FileUpload.Root>

                                    {previewUrls[fieldName] && (
                                        <Box mt={2}>
                                            <img
                                                src={previewUrls[fieldName]!}
                                                alt={`Preview ${fieldName}`}
                                                style={{ maxWidth: '100%', maxHeight: '200px' }}
                                            />
                                        </Box>
                                    )}
                                </Field>
                            ))}
                        </VStack>
                    </DialogBody>

                    <DialogFooter gap={2}>
                        <DialogActionTrigger asChild>
                            <Button
                                variant="subtle"
                                colorPalette="gray"
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                        </DialogActionTrigger>
                        <Button
                            variant="solid"
                            type="submit"
                            disabled={!isValid || isSubmitting}
                            loading={isSubmitting || mutation.isPending}
                        >
                            Save
                        </Button>
                    </DialogFooter>
                </form>
                <DialogCloseTrigger />
            </DialogContent>
        </DialogRoot>
    )
}

export default AddActivity
