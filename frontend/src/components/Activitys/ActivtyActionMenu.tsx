import { IconButton } from "@chakra-ui/react"
import { BsThreeDotsVertical } from "react-icons/bs"
import { MenuContent, MenuRoot, MenuTrigger } from "../ui/menu"
import type { Blog } from "../../client"
import EditActivity from "./EditActivity"
import DeleteActivity from "./DeleteActivity"
//import DeleteIntroduction from "./DeleteIntroduction"

interface ActivityActionsMenuProps {
    activity: Blog
}

export const ActivityActionMenu = ({ activity }: ActivityActionsMenuProps) => {
    return (
        <MenuRoot>
            <MenuTrigger asChild>
                <IconButton variant="ghost" color="inherit">
                    <BsThreeDotsVertical />
                </IconButton>
            </MenuTrigger>
            <MenuContent>
                <EditActivity blog={activity} />
                <DeleteActivity id={activity.id as number} />
            </MenuContent>
        </MenuRoot>
    )
}
