import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"
import {
    Button,
    DialogActionTrigger,
    DialogTitle,
    Input,
    Text,
    VStack,
    FileUpload,
    Box,
    Icon,
    Textarea,
} from "@chakra-ui/react"
import { useState, useEffect } from "react"
import { FaExchangeAlt, FaCloudUploadAlt, FaWindowClose } from "react-icons/fa"
import React from 'react'
import {
    type BlogsEditBlogData,
    type Blog,
    type Body_blogs_edit_blog,
    BlogsService
} from "../../client"
import type { ApiError } from "../../client/core/ApiError"
import useCustomToast from "../../hooks/useCustomToast"
import { handleError } from "../../utils"
import {
    DialogBody,
    DialogCloseTrigger,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogRoot,
    DialogTrigger,
} from "../ui/dialog"
import { Field } from "../ui/field"

interface EditActivityprops {
    blog: Blog
    onDeleteImage?: (updatedBlog: Blog) => void
}


const EditActivity = ({ blog, onDeleteImage }: EditActivityprops) => {
    const [isOpen, setIsOpen] = useState(false)
    const queryClient = useQueryClient()
    const { showSuccessToast } = useCustomToast()
    const {
        register,
        handleSubmit,
        reset,
        setValue,
        formState: { errors, isValid, isSubmitting },
    } = useForm<Body_blogs_edit_blog>({
        mode: "onBlur",
        criteriaMode: "all",
        defaultValues: {
            title: blog.title,
            description: blog.description || undefined,
            image1: undefined,
            image2: undefined,
            image3: undefined,
        },
    })

    // Watch for file changes

    const [previewUrls, setPreviewUrls] = useState<Record<string, string | null>>({
        image1: blog.image1 ? "https://images.philip-cat.com/" + blog.image1 : null,
        image2: blog.image2 ? "https://images.philip-cat.com/" + blog.image2 : null,
        image3: blog.image3 ? "https://images.philip-cat.com/" + blog.image3 : null
    })
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  

    const handleDeleteImage = async (fieldName: "image1" | "image2" | "image3") => {
        const imagePath = blog[fieldName];
        if (!imagePath) return;

        try {
            await BlogsService.deleteBlogImages({
                id: blog.id!,
                formData: { image: imagePath }
            })
            
            // Create a new blog object with the image removed
            const updatedBlog = { 
                ...blog, 
                [fieldName]: null,
                updated_at: new Date().toISOString() 
            }
            
            // Update local state
            setPreviewUrls(prev => ({ ...prev, [fieldName]: null }))
            setValue(fieldName, undefined, { shouldValidate: true })
            
            // Notify parent component
            onDeleteImage?.(updatedBlog)
            
            // Update local blog reference
            blog[fieldName] = null
            
            showSuccessToast("Image deleted successfully")
            console.log(blog)
        } catch (err) {
            handleError(err as ApiError)
        }
    }

    const handleFileChange = (
        event: React.ChangeEvent<HTMLInputElement>,
        fieldName: "image1" | "image2" | "image3"
    ) => {
        if (blog[fieldName]) {
            setShowDeleteConfirm(true)
            event.target.value = '' // Clear file input
            return
        }

        const file = event.target.files?.[0]
        if (file) {
            setValue(fieldName, file, { shouldValidate: true })
            const url = URL.createObjectURL(file)
            setPreviewUrls(prev => ({ ...prev, [fieldName]: url }))
        }
    }

    useEffect(() => {
        return () => {
            Object.values(previewUrls).forEach(url => {
                if (url) URL.revokeObjectURL(url)
            })
        }
    }, [previewUrls])
    const mutation = useMutation({
        mutationFn: (data: BlogsEditBlogData) => {
            // The API client expects an object with specific properties
            // The image should be the File object from the file input
            if (!blog.id) {
                throw new Error('Introduction ID is required');
            }
            return BlogsService.blogsEditBlog({
                id: blog.id,
                formData: data.formData
            });
        }, onSuccess: () => {
            showSuccessToast("Item created successfully.")
            reset()
            setIsOpen(false)
        },
        onError: (err: ApiError) => {
            handleError(err)
        },
        onSettled: () => {
            queryClient.invalidateQueries({ queryKey: ["activity"] })
        },
    })

    const onSubmit: SubmitHandler<Body_blogs_edit_blog> = (data) => {
        // Create clean formData object with only defined files
        const formData: Body_blogs_edit_blog = {
            title: data.title,
            description: data.description || '',
        };
        
        // Only include image fields that have actual File objects
        if (data.image1 instanceof File) formData.image1 = data.image1;
        if (data.image2 instanceof File) formData.image2 = data.image2;
        if (data.image3 instanceof File) formData.image3 = data.image3;

        const requestData: BlogsEditBlogData = {
            id: blog.id!,
            formData: formData
        };

        mutation.mutate(requestData);
    }

    return (
        <DialogRoot
            size={{ base: "xs", md: "md" }}
            placement="center"
            open={isOpen}
            onOpenChange={({ open }) => {
                setIsOpen(open);
                // If dialog is closing, reset the form
                if (!open) {
                    reset();
                    // If you're using preview URL state, reset that too
                    Object.values(previewUrls).forEach(url => {
                        if (url) URL.revokeObjectURL(url);
                    });
                    setPreviewUrls({
                        image1: null,
                        image2: null,
                        image3: null
                    });
                }
            }}
        >
            <DialogTrigger asChild>
                <Button variant="ghost">
                    <FaExchangeAlt fontSize="16px" />
                    Edit Activity
                </Button>
            </DialogTrigger>
            <DialogContent>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogHeader>
                        <DialogTitle>Add Activity</DialogTitle>
                    </DialogHeader>
                    <DialogBody>
                        <Text mb={4}>Fill in the details to add a new Activity.</Text>
                        <VStack gap={4}>
                            <Field
                                required
                                invalid={!!errors.title}
                                errorText={errors.title?.message}
                                label="Title"
                            >
                                <Input
                                    id="title"
                                    {...register("title", {
                                        required: "Title is required.",
                                    })}
                                    placeholder="Title"
                                    type="text"
                                />
                            </Field>

                            <Field
                                required
                                invalid={!!errors.description}
                                errorText={errors.description?.message}
                                label="Description"
                            >
                                <Textarea
                                    h="200px"
                                    id="description"
                                    {...register("description")}
                                    placeholder="Description"
                                />

                            </Field>

                            {(["image1", "image2", "image3"] as const).map((fieldName) => (
                                <Field
                                    key={fieldName}
                                    invalid={!!errors[fieldName]}
                                    errorText={errors[fieldName]?.message}
                                    label={`Image ${fieldName.charAt(5)}`}
                                >
                                    <FileUpload.Root accept={["image/png", "image/jpeg"]}>
                                        <FileUpload.HiddenInput
                                            {...register(fieldName)}
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => handleFileChange(e, fieldName)}
                                        />
                                        <FileUpload.Trigger asChild>
                                            <Button variant="outline" size="sm">
                                                <FaCloudUploadAlt /> Upload {fieldName}
                                            </Button>
                                        </FileUpload.Trigger>
                                        {!blog[fieldName] && <FileUpload.List />}
                                    </FileUpload.Root>

                                    {previewUrls[fieldName] && (
                                        <Box mt={2} position="relative">
                                            <img
                                                src={previewUrls[fieldName]!}
                                                alt={`Preview ${fieldName}`}
                                                style={{ maxWidth: '100%', maxHeight: '200px' }}
                                            />
                                            {blog[fieldName] && (
                                                <Icon
                                                    as={FaWindowClose}
                                                    position="absolute"
                                                    top={2}
                                                    right={2}
                                                    cursor="pointer"
                                                    color="red.500"
                                                    onClick={() => handleDeleteImage(fieldName)}
                                                />
                                            )}
                                        </Box>
                                    )}
                                </Field>
                            ))}

                            <DialogRoot
                                open={showDeleteConfirm}
                                onOpenChange={({ open }) => setShowDeleteConfirm(open)}
                            >
                                <DialogContent>
                                    <DialogHeader>
                                        <DialogTitle>Delete Image First</DialogTitle>
                                    </DialogHeader>
                                    <DialogBody>
                                        <Text>Please delete the existing image first before uploading a new one.</Text>
                                    </DialogBody>
                                    <DialogFooter>
                                        <Button onClick={() => setShowDeleteConfirm(false)}>
                                            OK
                                        </Button>
                                    </DialogFooter>
                                </DialogContent>
                            </DialogRoot>
                        </VStack>
                    </DialogBody>

                    <DialogFooter gap={2}>
                        <DialogActionTrigger asChild>
                            <Button
                                variant="subtle"
                                colorPalette="gray"
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                        </DialogActionTrigger>
                        <Button
                            variant="solid"
                            type="submit"
                            disabled={!isValid || isSubmitting}
                            loading={isSubmitting || mutation.isPending}
                        >
                            Save
                        </Button>
                    </DialogFooter>
                </form>
                <DialogCloseTrigger />
            </DialogContent>
        </DialogRoot>
    )
}
export default EditActivity
