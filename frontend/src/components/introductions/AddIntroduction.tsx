import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"
import {
    Button,
    DialogActionTrigger,
    DialogTitle,
    Input,
    Text,
    VStack,
    NumberInput,
    FileUpload,
    Box,
    Icon,
    Textarea,
    Flex,
} from "@chakra-ui/react"
import { useState, useEffect } from "react"
import { FaPlus, FaCloudUploadAlt } from "react-icons/fa"
import { Reorder } from "framer-motion";
import { type Body_introduction_create_introduction, IntroductionService } from "../../client"
import type { ApiError } from "../../client/core/ApiError"
import useCustomToast from "../../hooks/useCustomToast"
import { handleError } from "../../utils"
import {
    DialogBody,
    DialogCloseTrigger,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogRoot,
    DialogTrigger,
} from "../ui/dialog"
import { Field } from "../ui/field"

const initialItems = ["🍅 Tomato", "🥒 Cucumber", "🧀 Cheese", "🥬 Lettuce"];
const AddIntroduction = () => {
    const [isOpen, setIsOpen] = useState(false)
    const queryClient = useQueryClient()
    const { showSuccessToast, showErrorToast } = useCustomToast()
    const {
        register,
        handleSubmit,
        reset,
        setValue,
        formState: { errors, isValid, isSubmitting },
    } = useForm<Body_introduction_create_introduction>({
        mode: "onBlur",
        criteriaMode: "all",
        defaultValues: {
            index: 0,
            title: "",
            description: "",
            image: undefined,
        },
    })

    // Watch for file changes

    const [previewUrl, setPreviewUrl] = useState<string | null>(null)

    // Handle file changes manually to ensure we get the right format
    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (file) {
            // Set the form value directly
            setValue("image", file, { shouldValidate: true })

            // Create preview
            const url = URL.createObjectURL(file)
            setPreviewUrl(url)
        }
    }

    useEffect(() => {
        // Cleanup function
        return () => {
            if (previewUrl) URL.revokeObjectURL(previewUrl)
        }
    }, [previewUrl])
    const mutation = useMutation({
        mutationFn: (data: Body_introduction_create_introduction) => {
            // The API client expects an object with specific properties
            // The image should be the File object from the file input
            return IntroductionService.introductionCreateIntroduction({
                formData: {
                    index: data.index,
                    title: data.title,
                    description: data.description || '',
                    image: data.image || undefined,
                }
            });
        },
        onSuccess: () => {
            showSuccessToast("Item created successfully.")
            reset()
            if (previewUrl) {
                URL.revokeObjectURL(previewUrl);
                setPreviewUrl(null);
            }

            setIsOpen(false)
        },
        onError: (err: ApiError) => {
            handleError(err)
        },
        onSettled: () => {
            queryClient.invalidateQueries({ queryKey: ["introductions"] })
        },
    })

    const onSubmit: SubmitHandler<Body_introduction_create_introduction> = (data) => {
        if (!data.image) {
            // Set error manually
            showErrorToast("Image is required.")
            return;
        }

        mutation.mutate(data)
    }

    const [items, setItems] = useState(initialItems);

    return (
        <DialogRoot
            size={{ base: "xs", md: "md" }}
            placement="center"
            open={isOpen}
            onOpenChange={({ open }) => {
                setIsOpen(open);
                // If dialog is closing, reset the form
                if (!open) {
                    reset();
                    // If you're using preview URL state, reset that too
                    if (previewUrl) {
                        URL.revokeObjectURL(previewUrl);
                        setPreviewUrl(null);
                    }
                }
            }}
        >
            <DialogTrigger asChild>
                <Button value="add-item" my={4}>
                    <FaPlus fontSize="16px" />
                    Add Introduction
                </Button>
            </DialogTrigger>
            <DialogContent>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogHeader>
                        <DialogTitle>Add Introduction</DialogTitle>
                    </DialogHeader>
                    <DialogBody>
                        <Text mb={4}>Fill in the details to add a new Introduction.</Text>
                        <VStack gap={4}>
                            <Field
                                required
                                invalid={!!errors.title}
                                errorText={errors.title?.message}
                                label="Title"
                            >
                                <Input
                                    id="title"
                                    {...register("title", {
                                        required: "Title is required.",
                                    })}
                                    placeholder="Title"
                                    type="text"
                                />
                            </Field>

                            <Field
                                required
                                invalid={!!errors.description}
                                errorText={errors.description?.message}
                                label="Description"
                            >
                                <Textarea
                                    h="200px"
                                    id="description"
                                    {...register("description")}
                                    placeholder="Description"
                                />

                            </Field>
                            <Field
                                required
                                invalid={!!errors.index}
                                errorText={errors.index?.message}
                                label="Index"
                            >
                                <NumberInput.Root defaultValue="1" width="200px">
                                    <NumberInput.Control />
                                    <NumberInput.Input id="index"
                                        {...register("index")}
                                        placeholder="index"
                                        type="text"
                                    />
                                </NumberInput.Root>
                            </Field>
                            <Field
                                invalid={!!errors.image}
                                errorText={errors.image?.message}
                                label="image"
                            >

                                <FileUpload.Root maxW="xl" alignItems="stretch" >
                                    <FileUpload.HiddenInput
                                        {...register("image",)}
                                        type="file"  // Changed from "image" to "file"
                                        accept="image/*"
                                        onChange={handleFileChange}  // Add manual handler
                                    />
                                    <FileUpload.Dropzone>
                                        <Icon size="md" color="fg.muted">
                                            <FaCloudUploadAlt />
                                        </Icon>
                                        <FileUpload.DropzoneContent>
                                            <Box>Drag and drop files here</Box>
                                            <Box color="fg.muted">.png, .jpg up to 5MB</Box>
                                        </FileUpload.DropzoneContent>
                                    </FileUpload.Dropzone>
                                </FileUpload.Root>
                                {previewUrl && (
                                    <Box mt={2}>
                                        <img
                                            src={previewUrl}
                                            alt="Preview"
                                            style={{ maxWidth: '100%', maxHeight: '200px' }}
                                        />
                                    </Box>
                                )}

                            </Field>
                        </VStack>
                    </DialogBody>

                    <DialogFooter gap={2}>
                        <DialogActionTrigger asChild>
                            <Button
                                variant="subtle"
                                colorPalette="gray"
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                        </DialogActionTrigger>
                        <Button
                            variant="solid"
                            type="submit"
                            disabled={!isValid || isSubmitting}
                            loading={isSubmitting || mutation.isPending}
                        >
                            Save
                        </Button>
                    </DialogFooter>
                </form>
                <DialogCloseTrigger />
            </DialogContent>
        </DialogRoot>
    )
}

export default AddIntroduction
