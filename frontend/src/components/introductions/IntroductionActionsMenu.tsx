import { IconButton } from "@chakra-ui/react"
import { BsThreeDotsVertical } from "react-icons/bs"
import { MenuContent, MenuRoot, MenuTrigger } from "../ui/menu"
import type { Introduction } from "../../client"
import EditIntroduction from "./EditIntroduction"
import DeleteIntroduction from "./DeleteIntroduction"

interface IntroductionActionsMenuProps {
  introduction: Introduction
}

export const IntroductionActionsMenu = ({ introduction }: IntroductionActionsMenuProps) => {
  return (
    <MenuRoot>
      <MenuTrigger asChild>
        <IconButton variant="ghost" color="inherit">
          <BsThreeDotsVertical />
        </IconButton>
      </MenuTrigger>
      <MenuContent>
        <EditIntroduction introduction={introduction} />
        <DeleteIntroduction id={introduction.id as number} />
      </MenuContent>
    </MenuRoot>
  )
}
