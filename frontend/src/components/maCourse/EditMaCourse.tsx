import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"
import {
    Button,
    DialogActionTrigger,
    DialogTitle,
    Input,
    Text,
    VStack,
    NumberInput,
    FileUpload,
    Box,
    Icon,
    Textarea,
} from "@chakra-ui/react"
import { useState, useEffect } from "react"
import { FaExchangeAlt, FaCloudUploadAlt } from "react-icons/fa"
import React from 'react'
import { type MaCourseEditMacourseData, type MaCourseImage, type Body_ma_course_edit_macourse, MaCourseService } from "../../client"
import type { ApiError } from "../../client/core/ApiError"
import useCustomToast from "../../hooks/useCustomToast"
import { handleError } from "../../utils"
import {
    DialogBody,
    DialogCloseTrigger,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogRoot,
    DialogTrigger,
} from "../ui/dialog"
import { Field } from "../ui/field"

interface EditMaCourseprops {
    maCourse: MaCourseImage
}


const EditMaCourse = ({ maCourse }: EditMaCourseprops) => {
    const [isOpen, setIsOpen] = useState(false)
    const queryClient = useQueryClient()
    const { showSuccessToast } = useCustomToast()
    const {
        register,
        handleSubmit,
        reset,
        setValue,
        formState: { errors, isValid, isSubmitting },
    } = useForm<Body_ma_course_edit_macourse>({
        mode: "onBlur",
        criteriaMode: "all",
        defaultValues: {
            index: maCourse.index,
        },
    })

    // Watch for file changes



    const mutation = useMutation({
        mutationFn: (data: MaCourseEditMacourseData) => {
            // The API client expects an object with specific properties
            // The image should be the File object from the file input
            if (!maCourse.id) {
                throw new Error('Introduction ID is required');
            }
            return MaCourseService.editMacourse({
                id: maCourse.id,
                formData: data.formData
            });
        }, onSuccess: () => {
            showSuccessToast("Item created successfully.")
            reset()
            setIsOpen(false)
        },
        onError: (err: ApiError) => {
            handleError(err)
        },
        onSettled: () => {
            queryClient.invalidateQueries({ queryKey: ["maCourse"] })
        },
    })

    const onSubmit: SubmitHandler<Body_ma_course_edit_macourse> = (data) => {
        // Create a copy of the form data
        console.log(data)
        const formData = { ...data };


        // Remove image if it's an empty FileList
        const requestData: MaCourseEditMacourseData = {
            id: maCourse.id!,
            formData: formData
        };

        mutation.mutate(requestData);
    }

    return (
        <DialogRoot
            size={{ base: "xs", md: "md" }}
            placement="center"
            open={isOpen}
            onOpenChange={({ open }) => {
                setIsOpen(open);
                // If dialog is closing, reset the form
                if (!open) {
                    reset();
                }
            }}
        >
            <DialogTrigger asChild>
                <Button variant="ghost">
                    <FaExchangeAlt fontSize="16px" />
                    Edit Index
                </Button>
            </DialogTrigger>
            <DialogContent>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogHeader>
                        <DialogTitle>Add Image</DialogTitle>
                    </DialogHeader>
                    <DialogBody>
                        <Text mb={4}>Fill in the details to add a new Image.</Text>
                        <VStack gap={4}>

                            <Field
                                required
                                invalid={!!errors.index}
                                errorText={errors.index?.message}
                                label="Index"
                            >
                                <NumberInput.Root defaultValue="1" width="200px">
                                    <NumberInput.Control />
                                    <NumberInput.Input id="index"
                                        {...register("index")}
                                        placeholder="index"
                                        type="text"
                                    />
                                </NumberInput.Root>
                            </Field>

                        </VStack>
                    </DialogBody>

                    <DialogFooter gap={2}>
                        <DialogActionTrigger asChild>
                            <Button
                                variant="subtle"
                                colorPalette="gray"
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                        </DialogActionTrigger>
                        <Button
                            variant="solid"
                            type="submit"
                            disabled={!isValid || isSubmitting}
                            loading={isSubmitting || mutation.isPending}
                        >
                            Save
                        </Button>
                    </DialogFooter>
                </form>
                <DialogCloseTrigger />
            </DialogContent>
        </DialogRoot>
    )
}
export default EditMaCourse
