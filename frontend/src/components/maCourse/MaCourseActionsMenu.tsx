import { Icon<PERSON>utton } from "@chakra-ui/react"
import { BsThreeDotsVertical } from "react-icons/bs"
import { MenuContent, MenuRoot, MenuTrigger } from "../ui/menu"
import type { MaCourseImage } from "../../client"
import EditMaCourse from "./EditMaCourse"
import DeleteMaCourse from "./DeleteMaCourse"
import React from "react"
interface MaCourseActionsMenuProps {
  maCourse: MaCourseImage
}

export const MaCourseActionsMenu = ({ maCourse }: MaCourseActionsMenuProps) => {
  return (
    <MenuRoot>
      <MenuTrigger asChild>
        <IconButton variant="ghost" color="inherit">
          <BsThreeDotsVertical />
        </IconButton>
      </MenuTrigger>
      <MenuContent>
        <EditMaCourse maCourse={maCourse} />
        <DeleteMaCourse id={maCourse.id as string} />
      </MenuContent>
    </MenuRoot>
  )
}
