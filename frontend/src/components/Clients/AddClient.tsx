import { useState } from "react"
import {
  But<PERSON>,
  Text,
  VStack,
  Input,
} from "@chakra-ui/react"
import { Field } from "@/components/ui/field"
import { Checkbox } from "@/components/ui/checkbox"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"
import { FaPlus } from "react-icons/fa"

import useCustomToast from "@/hooks/useCustomToast"
import { AdminService, type ClientCreate } from "@/client"
import {
  DialogActionTrigger,
  DialogBody,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

interface ClientCreateForm extends ClientCreate {
  confirm_password: string
}

const AddClient = () => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()
  const {
    register,
    handleSubmit,
    reset,
    getValues,
    formState: { errors, isValid, isSubmitting },
  } = useForm<ClientCreateForm>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: {
      email: "",
      full_name: "",
      phone_number: "",
      password: "",
      confirm_password: "",
      is_active: true,
    },
  })

  const mutation = useMutation({
    mutationFn: (data: ClientCreateForm) => {
      const { confirm_password, ...clientData } = data
      return AdminService.adminCreateClientAdmin({ requestBody: clientData })
    },
    onSuccess: () => {
      showSuccessToast("Client created successfully.")
      reset()
      setIsOpen(false)
    },
    onError: (err: Error) => {
      const { showErrorToast } = useCustomToast()
      showErrorToast(err.message || "Failed to create client")
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] })
    },
  })

  const onSubmit: SubmitHandler<ClientCreateForm> = (data) => {
    mutation.mutate(data)
  }

  return (
    <DialogRoot
      size={{ base: "xs", md: "md" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => setIsOpen(open)}
    >
      <DialogTrigger asChild>
        <Button value="add-client" my={4}>
          <FaPlus fontSize="16px" />
          Add Client
        </Button>
      </DialogTrigger>
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Add Client</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <Text mb={4}>Fill in the details to add a new client.</Text>
            <VStack gap={4}>
              <Field
                required
                invalid={!!errors.email}
                errorText={errors.email?.message}
                label="Email"
              >
                <Input
                  id="email"
                  {...register("email", {
                    required: "Email is required.",
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: "Email format is invalid.",
                    },
                  })}
                  placeholder="Email"
                  type="email"
                />
              </Field>
              <Field
                invalid={!!errors.full_name}
                errorText={errors.full_name?.message}
                label="Full Name"
              >
                <Input
                  id="full_name"
                  {...register("full_name")}
                  placeholder="Full Name"
                  type="text"
                />
              </Field>
              <Field
                invalid={!!errors.phone_number}
                errorText={errors.phone_number?.message}
                label="Phone Number"
              >
                <Input
                  id="phone_number"
                  {...register("phone_number")}
                  placeholder="Phone Number"
                  type="tel"
                />
              </Field>
              <Field
                required
                invalid={!!errors.password}
                errorText={errors.password?.message}
                label="Password"
              >
                <Input
                  id="password"
                  {...register("password", {
                    required: "Password is required.",
                    minLength: {
                      value: 8,
                      message: "Password must be at least 8 characters.",
                    },
                  })}
                  placeholder="Password"
                  type="password"
                />
              </Field>
              <Field
                required
                invalid={!!errors.confirm_password}
                errorText={errors.confirm_password?.message}
                label="Confirm Password"
              >
                <Input
                  id="confirm_password"
                  {...register("confirm_password", {
                    required: "Password confirmation is required.",
                    validate: (value) =>
                      value === getValues().password || "Passwords do not match.",
                  })}
                  placeholder="Confirm Password"
                  type="password"
                />
              </Field>
              <Field label="Active Status">
                <Checkbox
                  {...register("is_active")}
                  defaultChecked={true}
                >
                  Active
                </Checkbox>
              </Field>
            </VStack>
          </DialogBody>
          <DialogFooter>
            <DialogActionTrigger asChild>
              <Button variant="outline">Cancel</Button>
            </DialogActionTrigger>
            <Button
              variant="solid"
              type="submit"
              loading={isSubmitting}
              disabled={!isValid}
            >
              Add Client
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </DialogRoot>
  )
}

export default AddClient
