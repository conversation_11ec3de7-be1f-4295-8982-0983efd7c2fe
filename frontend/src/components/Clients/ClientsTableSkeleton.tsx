import { Skeleton, Table } from "@chakra-ui/react"

const ClientsTableSkeleton = () => (
  <Table.Root size={{ base: "sm", md: "md" }}>
    <Table.Header>
      <Table.Row>
        <Table.ColumnHeader w="20%">Created At</Table.ColumnHeader>
        <Table.ColumnHeader w="25%">Name</Table.ColumnHeader>
        <Table.ColumnHeader w="25%">Email</Table.ColumnHeader>
        <Table.ColumnHeader w="15%">Phone</Table.ColumnHeader>
        <Table.ColumnHeader w="10%">Status</Table.ColumnHeader>
        <Table.ColumnHeader w="5%">Actions</Table.ColumnHeader>
      </Table.Row>
    </Table.Header>
    <Table.Body>
      {[...Array(5)].map((_, index) => (
        <Table.Row key={index}>
          <Table.Cell w="20%">
            <Skeleton h="20px" />
          </Table.Cell>
          <Table.Cell w="25%">
            <Skeleton h="20px" />
          </Table.Cell>
          <Table.Cell w="25%">
            <Skeleton h="20px" />
          </Table.Cell>
          <Table.Cell w="15%">
            <Skeleton h="20px" />
          </Table.Cell>
          <Table.Cell w="10%">
            <Skeleton h="20px" />
          </Table.Cell>
          <Table.Cell w="5%">
            <Skeleton h="20px" />
          </Table.Cell>
        </Table.Row>
      ))}
    </Table.Body>
  </Table.Root>
)

export default ClientsTableSkeleton
