import { useState } from "react"
import { <PERSON><PERSON>, Icon<PERSON>utton } from "@chakra-ui/react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"
import { FiEdit, FiMoreHorizontal, FiTrash2, FiEye } from "react-icons/fi"

import useCustomToast from "@/hooks/useCustomToast"
import { AdminService, type ClientPublic } from "@/client"
import {
  DialogActionTrigger,
  DialogBody,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from "@/components/ui/menu"
import EditClient from "./EditClient"

interface ClientActionsMenuProps {
  client: ClientPublic
}

const ClientActionsMenu = ({ client }: ClientActionsMenuProps) => {
  const [isEditOpen, setIsEditOpen] = useState(false)
  const [isDelete<PERSON><PERSON>, setIsDeleteOpen] = useState(false)
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const { showSuccessToast } = useCustomToast()

  const deleteMutation = useMutation({
    mutationFn: (clientId: string) => AdminService.adminDeleteClientAdmin({ clientId }),
    onSuccess: () => {
      showSuccessToast("Client deleted successfully.")
      setIsDeleteOpen(false)
    },
    onError: (err: Error) => {
      const { showErrorToast } = useCustomToast()
      showErrorToast(err.message || "Failed to delete client")
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] })
    },
  })

  const handleDelete = () => {
    deleteMutation.mutate(client.id)
  }

  const handleViewDetails = () => {
    navigate({ to: "/client/$clientId", params: { clientId: client.id } })
  }

  return (
    <>
      <MenuRoot>
        <MenuTrigger asChild>
          <IconButton
            variant="ghost"
            size="sm"
            aria-label="Client actions"
          >
            <FiMoreHorizontal />
          </IconButton>
        </MenuTrigger>
        <MenuContent>
          <MenuItem
            value="view"
            onClick={handleViewDetails}
          >
            <FiEye />
            View Details
          </MenuItem>
          <MenuItem
            value="edit"
            onClick={() => setIsEditOpen(true)}
          >
            <FiEdit />
            Edit
          </MenuItem>
          <MenuItem
            value="delete"
            onClick={() => setIsDeleteOpen(true)}
            color="red.500"
          >
            <FiTrash2 />
            Delete
          </MenuItem>
        </MenuContent>
      </MenuRoot>

      {/* Edit Dialog */}
      <EditClient
        client={client}
        isOpen={isEditOpen}
        onClose={() => setIsEditOpen(false)}
      />

      {/* Delete Confirmation Dialog */}
      <DialogRoot
        size="sm"
        placement="center"
        open={isDeleteOpen}
        onOpenChange={({ open }) => setIsDeleteOpen(open)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Client</DialogTitle>
          </DialogHeader>
          <DialogBody>
            Are you sure you want to delete the client{" "}
            <strong>{client.full_name || client.email}</strong>? This action
            cannot be undone.
          </DialogBody>
          <DialogFooter>
            <DialogActionTrigger asChild>
              <Button variant="outline">Cancel</Button>
            </DialogActionTrigger>
            <Button
              variant="solid"
              colorPalette="red"
              onClick={handleDelete}
              loading={deleteMutation.isPending}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>
    </>
  )
}

export default ClientActionsMenu
