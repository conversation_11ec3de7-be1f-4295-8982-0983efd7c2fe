import { useEffect } from "react"
import {
  But<PERSON>,
  Text,
  VStack,
  Input,
} from "@chakra-ui/react"
import { Field } from "@/components/ui/field"
import { Checkbox } from "@/components/ui/checkbox"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"

import useCustomToast from "@/hooks/useCustomToast"
import { AdminService, type ClientUpdate, type ClientPublic } from "@/client"
import {
  DialogActionTrigger,
  DialogBody,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
  DialogTitle,
} from "@/components/ui/dialog"

interface ClientUpdateForm extends ClientUpdate {
  email: string
  full_name: string
  phone_number: string
  is_active: boolean
}

interface EditClientProps {
  client: ClientPublic
  isOpen: boolean
  onClose: () => void
}

const EditClient = ({ client, isOpen, onClose }: EditClientProps) => {
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid, isSubmitting },
  } = useForm<ClientUpdateForm>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: {
      email: "",
      full_name: "",
      phone_number: "",
      is_active: true,
    },
  })

  // Reset form with client data when client changes or dialog opens
  useEffect(() => {
    if (client && isOpen) {
      reset({
        email: client.email,
        full_name: client.full_name || "",
        phone_number: client.phone_number || "",
        is_active: client.is_active,
      })
    }
  }, [client, isOpen, reset])

  const mutation = useMutation({
    mutationFn: (data: ClientUpdateForm) =>
      AdminService.adminUpdateClientAdmin({
        clientId: client.id,
        requestBody: data
      }),
    onSuccess: () => {
      showSuccessToast("Client updated successfully.")
      onClose()
    },
    onError: (err: Error) => {
      const { showErrorToast } = useCustomToast()
      showErrorToast(err.message || "Failed to update client")
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] })
    },
  })

  const onSubmit: SubmitHandler<ClientUpdateForm> = (data) => {
    mutation.mutate(data)
  }

  return (
    <DialogRoot
      size={{ base: "xs", md: "md" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => !open && onClose()}
    >
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Edit Client</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <Text mb={4}>Update the client information.</Text>
            <VStack gap={4}>
              <Field
                required
                invalid={!!errors.email}
                errorText={errors.email?.message}
                label="Email"
              >
                <Input
                  id="email"
                  {...register("email", {
                    required: "Email is required.",
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: "Email format is invalid.",
                    },
                  })}
                  placeholder="Email"
                  type="email"
                />
              </Field>
              <Field
                invalid={!!errors.full_name}
                errorText={errors.full_name?.message}
                label="Full Name"
              >
                <Input
                  id="full_name"
                  {...register("full_name")}
                  placeholder="Full Name"
                  type="text"
                />
              </Field>
              <Field
                invalid={!!errors.phone_number}
                errorText={errors.phone_number?.message}
                label="Phone Number"
              >
                <Input
                  id="phone_number"
                  {...register("phone_number")}
                  placeholder="Phone Number"
                  type="tel"
                />
              </Field>
              <Field label="Active Status">
                <Checkbox
                  {...register("is_active")}
                >
                  Active
                </Checkbox>
              </Field>
            </VStack>
          </DialogBody>
          <DialogFooter>
            <DialogActionTrigger asChild>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
            </DialogActionTrigger>
            <Button
              variant="solid"
              type="submit"
              loading={isSubmitting}
              disabled={!isValid}
            >
              Update Client
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </DialogRoot>
  )
}

export default EditClient
