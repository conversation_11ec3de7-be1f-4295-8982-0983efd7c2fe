import {
  Button,
  Input,
  VStack,
  Text,
  Alert,
  Box,
  Code,
} from "@chakra-ui/react"
import {
  DialogRoot,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogCloseTrigger,
  <PERSON>alog<PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog"
import { Field } from "@/components/ui/field"
import { useMutation } from "@tanstack/react-query"
import { useState } from "react"
import Papa from "papaparse"

import { QuestionnairesService, type QuestionImportRow } from "@/client"
import useCustomToast from "@/hooks/useCustomToast"

interface ImportQuestionsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  surveyTypeId: number
  surveyName: string
  onSuccess: () => void
}

const ImportQuestionsModal = ({
  open,
  onOpenChange,
  surveyTypeId,
  surveyName,
  onSuccess,
}: ImportQuestionsModalProps) => {
  const showToast = useCustomToast()
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [showWarning, setShowWarning] = useState(false)
  const [isParsing, setIsParsing] = useState(false)

  const mutation = useMutation({
    mutationFn: (questions: QuestionImportRow[]) =>
      QuestionnairesService.questionnairesImportSurveyQuestionsJson({
        surveyTypeId,
        requestBody: { questions }
      }),
    onSuccess: (data) => {
      setIsParsing(false)
      showToast.showSuccessToast(data.message)
      onSuccess()
      handleClose()
    },
    onError: (error: any) => {
      setIsParsing(false)
      const message = error?.response?.data?.detail || error.message || "Import failed"
      showToast.showErrorToast(message)
    },
  })

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // More flexible CSV file validation
      const isCSV = file.type === "text/csv" ||
                   file.type === "application/csv" ||
                   file.name.toLowerCase().endsWith(".csv")

      if (!isCSV) {
        showToast.showErrorToast("Please select a CSV file")
        event.target.value = '' // Clear the input
        return
      }

      // Check file size (limit to 10MB)
      if (file.size > 10 * 1024 * 1024) {
        showToast.showErrorToast("File size must be less than 10MB")
        event.target.value = '' // Clear the input
        return
      }

      setSelectedFile(file)
      setShowWarning(true)
    }
  }

  const handleImport = () => {
    if (!selectedFile) {
      showToast.showErrorToast("Please select a CSV file to import")
      return
    }

    setIsParsing(true)

    // Parse CSV file using Papa Parse with better error handling
    Papa.parse(selectedFile, {
      header: true,
      skipEmptyLines: true,
      encoding: 'UTF-8',
      complete: (results) => {
        try {
          // Check for parsing errors
          if (results.errors && results.errors.length > 0) {
            const errorMessages = results.errors.map(err => err.message).join(', ')
            showToast.showErrorToast(`CSV parsing errors: ${errorMessages}`)
            return
          }

          // Validate headers
          const expectedHeaders = ['Intelligence Category', 'Core Competency', 'Question']
          const headers = results.meta.fields || []
          const missingHeaders = expectedHeaders.filter(header => !headers.includes(header))

          if (missingHeaders.length > 0) {
            showToast.showErrorToast(`CSV must contain headers: ${expectedHeaders.join(', ')}`)
            return
          }

          // Convert parsed data to the expected format
          const questions: QuestionImportRow[] = results.data
            .filter((row: any) => row && row.Question && row.Question.trim()) // Skip empty questions
            .map((row: any) => ({
              intelligence_category: row['Intelligence Category']?.trim() || '',
              core_competency: row['Core Competency']?.trim() || '',
              question: row.Question?.trim() || ''
            }))

          if (questions.length === 0) {
            showToast.showErrorToast("No valid questions found in the CSV file")
            return
          }

          // Send parsed data to backend
          mutation.mutate(questions)
        } catch (error: any) {
          setIsParsing(false)
          showToast.showErrorToast(`Error processing CSV: ${error.message || 'Unknown error'}`)
        }
      },
      error: (error: any) => {
        setIsParsing(false)
        showToast.showErrorToast(`Error reading CSV file: ${error.message || 'File could not be read'}`)
      }
    })
  }

  const handleClose = () => {
    setSelectedFile(null)
    setShowWarning(false)
    setIsParsing(false)
    onOpenChange(false)
  }

  const handleOpenChange = (details: { open: boolean }) => {
    onOpenChange(details.open)
  }

  return (
    <DialogRoot open={open} onOpenChange={handleOpenChange} size="xl">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Import Questions</DialogTitle>
          <DialogCloseTrigger />
        </DialogHeader>
        <DialogBody>
          <VStack gap={6} align="stretch">
            <Box>
              <Text fontWeight="semibold" mb={2}>
                Survey: {surveyName}
              </Text>
              <Text fontSize="sm" color="gray.600">
                Import questions from a CSV file. The file will be parsed locally and sent as structured data.
              </Text>
            </Box>

            {showWarning && (
              <Alert.Root status="warning" borderRadius="md">
                <Alert.Indicator />
                <Alert.Content>
                  <Alert.Title>Warning!</Alert.Title>
                  <Alert.Description>
                    This will DELETE ALL existing questions for "{surveyName}" and replace them with the new data from your CSV file.
                  </Alert.Description>
                </Alert.Content>
              </Alert.Root>
            )}

            <Field label="CSV File">
              <Input
                type="file"
                accept=".csv"
                onChange={handleFileChange}
                p={1}
              />
              {selectedFile && (
                <Text fontSize="sm" color="green.600" mt={2}>
                  Selected: {selectedFile.name}
                </Text>
              )}
            </Field>

            <Box
              bg={{ base: "gray.50", _dark: "gray.800" }}
              p={4}
              borderRadius="md"
              border="1px"
              borderColor={{ base: "gray.200", _dark: "gray.600" }}
            >
              <Text fontWeight="semibold" mb={2}>
                CSV Format Requirements:
              </Text>
              <Text fontSize="sm" mb={2}>
                Your CSV file must contain exactly these headers:
              </Text>
              <Code display="block" p={2} mb={2}>
                Intelligence Category,Core Competency,Question
              </Code>
              <Text fontSize="sm" mb={2}>
                Example:
              </Text>
              <Code display="block" p={2} fontSize="xs">
                Logical-Mathematical,Problem Solving,What is 2+2?
                <br />
                Linguistic,Reading Comprehension,Read the following text...
              </Code>
              <Text fontSize="sm" mt={2} color="gray.600">
                • Index will be automatically assigned based on row number
                <br />
                • Intelligence Categories and Core Competencies will be created if they don't exist
                <br />
                • Empty questions will be skipped
              </Text>
            </Box>
          </VStack>
          </DialogBody>

          <DialogFooter>
            <Button variant="ghost" mr={3} onClick={handleClose}>
              Cancel
            </Button>
            <Button
              colorPalette="red"
              onClick={handleImport}
              loading={isParsing || mutation.isPending}
              loadingText={isParsing ? "Parsing CSV..." : "Importing..."}
              disabled={!selectedFile || isParsing || mutation.isPending}
            >
              Confirm Import
            </Button>
          </DialogFooter>
        </DialogContent>
    </DialogRoot>
  )
}

export default ImportQuestionsModal
