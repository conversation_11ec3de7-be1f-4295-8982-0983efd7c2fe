import { Badge } from "@chakra-ui/react"
import type { QuestionnaireStatusEnum } from "@/client"

interface QuestionnaireStatusBadgeProps {
  status: QuestionnaireStatusEnum
}

export default function QuestionnaireStatusBadge({ status }: QuestionnaireStatusBadgeProps) {
  const getStatusConfig = (status: QuestionnaireStatusEnum) => {
    switch (status) {
      case "available":
        return {
          colorScheme: "blue",
          label: "Available"
        }
      case "finished":
        return {
          colorScheme: "green", 
          label: "Finished"
        }
      default:
        return {
          colorScheme: "gray",
          label: "Unknown"
        }
    }
  }

  const config = getStatusConfig(status)

  return (
    <Badge
      colorScheme={config.colorScheme}
      size="sm"
      px={2}
      py={1}
      borderRadius="md"
    >
      {config.label}
    </Badge>
  )
}
