import {
  Button,
  Input,
  Textarea,
  VStack,
  Text,
  Box,
  Badge,
} from "@chakra-ui/react"
import {
  <PERSON><PERSON>Root,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogClose<PERSON>rigger,
  <PERSON>alog<PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog"
import { Field } from "@/components/ui/field"
import { useMutation } from "@tanstack/react-query"
import { useState, useEffect } from "react"

import { QuestionnairesService, type QuestionWithDetails, type QuestionUpdate } from "@/client"
import useCustomToast from "@/hooks/useCustomToast"

interface EditQuestionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  question: QuestionWithDetails
  onSuccess: () => void
}

const EditQuestionModal = ({
  open,
  onOpenChange,
  question,
  onSuccess,
}: EditQuestionModalProps) => {
  const showToast = useCustomToast()
  const [content, setContent] = useState("")
  const [index, setIndex] = useState("")

  useEffect(() => {
    if (question) {
      setContent(question.content)
      setIndex(question.index.toString())
    }
  }, [question])

  const mutation = useMutation({
    mutationFn: (data: QuestionUpdate) =>
      QuestionnairesService.questionnairesUpdateQuestion({
        questionId: question.question_id,
        requestBody: data
      }),
    onSuccess: () => {
      showToast.showSuccessToast("Question updated successfully")
      onSuccess()
      handleClose()
    },
    onError: (error: any) => {
      const message = error?.response?.data?.detail || error.message || "Update failed"
      showToast.showErrorToast(message)
    },
  })

  const handleSave = () => {
    const updateData: QuestionUpdate = {}
    
    if (content !== question.content) {
      updateData.content = content
    }
    
    const newIndex = parseInt(index)
    if (!isNaN(newIndex) && newIndex !== question.index) {
      updateData.index = newIndex
    }

    if (Object.keys(updateData).length === 0) {
      showToast.showErrorToast("No changes were made to the question")
      return
    }

    mutation.mutate(updateData)
  }

  const handleClose = () => {
    setContent(question.content)
    setIndex(question.index.toString())
    onOpenChange(false)
  }

  const handleOpenChange = (details: { open: boolean }) => {
    onOpenChange(details.open)
  }

  return (
    <DialogRoot open={open} onOpenChange={handleOpenChange} size="xl">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Question</DialogTitle>
          <DialogCloseTrigger />
        </DialogHeader>
        <DialogBody>
          <VStack gap={6} align="stretch">
            <Box>
              <Text fontWeight="semibold" mb={2}>
                Question ID: {question.question_id}
              </Text>
              <Text fontSize="sm" color="gray.600">
                Edit the question content and index. Intelligence Category and Core Competency cannot be changed.
              </Text>
            </Box>

            <Box
              bg={{ base: "gray.50", _dark: "gray.800" }}
              p={4}
              borderRadius="md"
              border="1px"
              borderColor={{ base: "gray.200", _dark: "gray.600" }}
            >
              <VStack gap={3} align="stretch">
                <Box>
                  <Text fontSize="sm" fontWeight="semibold" color="gray.700">
                    Intelligence Category
                  </Text>
                  <Badge variant="outline" colorPalette="blue">
                    {question.intelligence_category}
                  </Badge>
                </Box>
                <Box>
                  <Text fontSize="sm" fontWeight="semibold" color="gray.700">
                    Core Competency
                  </Text>
                  <Badge variant="outline" colorPalette="green">
                    {question.core_competency}
                  </Badge>
                </Box>
              </VStack>
            </Box>

            <Field
              label="Index"
              helperText="The order/position of this question in the survey"
            >
              <Input
                type="number"
                value={index}
                onChange={(e) => setIndex(e.target.value)}
                placeholder="Question index (order)"
              />
            </Field>

            <Field
              label="Question Content"
              helperText="The actual question text that will be displayed to users"
            >
              <Textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="Enter the question content..."
                rows={6}
                resize="vertical"
              />
            </Field>
          </VStack>
          </DialogBody>

          <DialogFooter>
            <Button variant="ghost" mr={3} onClick={handleClose}>
              Cancel
            </Button>
            <Button
              colorPalette="blue"
              onClick={handleSave}
              loading={mutation.isPending}
              loadingText="Saving..."
              disabled={!content.trim()}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
    </DialogRoot>
  )
}

export default EditQuestionModal
