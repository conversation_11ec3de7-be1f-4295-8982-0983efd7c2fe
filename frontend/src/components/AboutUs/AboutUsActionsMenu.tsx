import { IconButton } from "@chakra-ui/react"
import { BsThreeDotsVertical } from "react-icons/bs"
import { MenuContent, MenuRoot, MenuTrigger } from "../ui/menu"
import type { AboutUs } from "../../client"
import EditAboutUs from "./EditAboutUs"
import DeleteAboutUs from "./DeleteAboutUs"
import React from 'react'

interface AboutUsActionsMenuProps {
  aboutUs: AboutUs
}

export const AboutUsActionsMenu = ({ aboutUs }: AboutUsActionsMenuProps) => {
  return (
    <MenuRoot>
      <MenuTrigger asChild>
        <IconButton variant="ghost" color="inherit">
          <BsThreeDotsVertical />
        </IconButton>
      </MenuTrigger>
      <MenuContent>
        <EditAboutUs aboutUs={aboutUs} />
        <DeleteAboutUs id={aboutUs.id as number} />
      </MenuContent>
    </MenuRoot>
  )
}
