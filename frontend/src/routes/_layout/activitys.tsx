import {
    Container,
    EmptyState,
    Flex,
    Heading,
    Table,
    VStack,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { FiSearch } from "react-icons/fi"
import { z } from "zod"
import moment from 'moment'
import { BlogsService } from "@/client"
import { ActivityActionMenu } from "@/components/Activitys/ActivtyActionMenu"
import AddAtivity from "@/components/activitys/AddActivity"
import PendingItems from "@/components/Pending/PendingItems"
import {
    PaginationItems,
    PaginationNextTrigger,
    PaginationPrevTrigger,
    PaginationRoot,
} from "../../components/ui/pagination.tsx"

const activitysSearchSchema = z.object({
    page: z.number().catch(1),
})

const PER_PAGE = 20

function getActivitysQueryOptions({ page }: { page: number }) {
    return {
        queryFn: () =>
            BlogsService.getBlogs({ skip: (page - 1) * PER_PAGE, limit: PER_PAGE }),
        queryKey: ["activity", { page }],
    }
}

export const Route = createFileRoute("/_layout/activitys")({
    component: Activitys,
    validateSearch: (search) => activitysSearchSchema.parse(search),
})

function ActivitysTable() {
    const navigate = useNavigate({ from: Route.fullPath })
    const { page } = Route.useSearch()

    const { data, isLoading, isPlaceholderData } = useQuery({
        ...getActivitysQueryOptions({ page }),
        placeholderData: (prevData) => prevData,
    })

    const setPage = (page: number) =>
        navigate({
            search: (prev: { [key: string]: string }) => ({ ...prev, page }),
        })

    const items = data?.data.slice(0, PER_PAGE) ?? []
    const count = data?.count ?? 0

    // Add sorting by created_at date (newest first) using moment
    const sortedItems = [...items].sort((a, b) => {
        // Use moment to handle possible undefined dates
        const dateA = a.created_at ? moment(a.created_at) : moment(0); // fallback to epoch time
        const dateB = b.created_at ? moment(b.created_at) : moment(0);

        // Sort in descending order (newest first)
        return dateB.valueOf() - dateA.valueOf();
    });

    if (isLoading) {
        return <PendingItems />
    }

    if (items.length === 0) {
        return (
            <EmptyState.Root>
                <EmptyState.Content>
                    <EmptyState.Indicator>
                        <FiSearch />
                    </EmptyState.Indicator>
                    <VStack textAlign="center">
                        <EmptyState.Title>You don't have any Activitys yet</EmptyState.Title>
                        <EmptyState.Description>
                            Add a new Activitys to get started
                        </EmptyState.Description>
                    </VStack>
                </EmptyState.Content>
            </EmptyState.Root>
        )
    }

    return (
        <>
            <Table.Root size={{ base: "sm", md: "md" }}>
                <Table.Header>
                    <Table.Row>
                        <Table.ColumnHeader w="5%">ID</Table.ColumnHeader>
                        <Table.ColumnHeader w="30%">Title</Table.ColumnHeader>
                        <Table.ColumnHeader w="50%">Description</Table.ColumnHeader>
                        <Table.ColumnHeader w="5%">Date</Table.ColumnHeader>
                        <Table.ColumnHeader w="10%">Actions</Table.ColumnHeader>
                    </Table.Row>
                </Table.Header>
                <Table.Body>
                    {sortedItems?.map((item) => (
                        <Table.Row key={item.id} opacity={isPlaceholderData ? 0.5 : 1}>
                            <Table.Cell truncate maxW="5%">
                                {item.id}
                            </Table.Cell>
                            <Table.Cell truncate maxW="30%">
                                {item.title}
                            </Table.Cell>
                            <Table.Cell
                                color={!item.description ? "gray" : "inherit"}
                                truncate
                                maxW="50%"
                            >
                                {item.description
                                    ? (item.description.length > 50
                                        ? `${item.description.substring(0, 50)}...`
                                        : item.description)
                                    : "N/A"}
                            </Table.Cell>
                            <Table.Cell truncate maxW="5%">
                                {moment(item.created_at).format('DD-MM-YYYY')}
                            </Table.Cell>
                            <Table.Cell width="10%">
                                <ActivityActionMenu activity={item} />
                            </Table.Cell>
                        </Table.Row>
                    ))}
                </Table.Body>
            </Table.Root>
            <Flex justifyContent="flex-end" mt={4}>
                <PaginationRoot
                    count={count}
                    pageSize={PER_PAGE}
                    onPageChange={({ page }) => setPage(page)}
                >
                    <Flex>
                        <PaginationPrevTrigger />
                        <PaginationItems />
                        <PaginationNextTrigger />
                    </Flex>
                </PaginationRoot>
            </Flex>
        </>
    )
}

function Activitys() {
    return (
        <Container maxW="full">
            <Heading size="lg" pt={12}>
                Activitys Management
            </Heading>
            <AddAtivity />
            <ActivitysTable />
        </Container>
    )
}
