import {
    Container,
    EmptyState,
    Flex,
    Heading,
    Table,
    VStack,
    Image,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { FiSearch } from "react-icons/fi"
import { z } from "zod"

import { MaCourseService } from "@/client"
import { MaCourseActionsMenu } from "@/components/maCourse/MaCourseActionsMenu"
import AddMaCourse from "@/components/maCourse/AddMaCourse"
import PendingItems from "@/components/Pending/PendingItems"
import {
    PaginationItems,
    PaginationNextTrigger,
    PaginationPrevTrigger,
    PaginationRoot,
} from "../../components/ui/pagination.tsx"
const imageBaseUrl = import.meta.env.VITE_IMAGE_URL;
const itemsSearchSchema = z.object({
    page: z.number().catch(1),
})

const PER_PAGE = 20

function getItemsQueryOptions({ page }: { page: number }) {
    return {
        queryFn: () =>
            MaCourseService.maCourseGetMacourses({ skip: (page - 1) * PER_PAGE, limit: PER_PAGE }),
        queryKey: ["maCourse", { page }],
    }
}

export const Route = createFileRoute("/_layout/maCourse")({
    component: MaCourse,
    validateSearch: (search) => itemsSearchSchema.parse(search),
})

function ItemsTable() {
    const navigate = useNavigate({ from: Route.fullPath })
    const { page } = Route.useSearch()

    const { data, isLoading, isPlaceholderData } = useQuery({
        ...getItemsQueryOptions({ page }),
        placeholderData: (prevData) => prevData,
    })

    const setPage = (page: number) =>
        navigate({
            search: (prev: { [key: string]: string }) => ({ ...prev, page }),
        })

    const items = data?.data.slice(0, PER_PAGE) ?? []
    const count = data?.count ?? 0

    console.log(items)

    if (isLoading) {
        return <PendingItems />
    }

    if (items.length === 0) {
        return (
            <EmptyState.Root>
                <EmptyState.Content>
                    <EmptyState.Indicator>
                        <FiSearch />
                    </EmptyState.Indicator>
                    <VStack textAlign="center">
                        <EmptyState.Title>You don't have any Image yet</EmptyState.Title>
                        <EmptyState.Description>
                            Add a new Ma Course Image to get started
                        </EmptyState.Description>
                    </VStack>
                </EmptyState.Content>
            </EmptyState.Root>
        )
    }

    return (
        <>
            <Table.Root size={{ base: "sm", md: "md" }}>
                <Table.Header>
                    <Table.Row>
                        <Table.ColumnHeader w="5%">ID</Table.ColumnHeader>
                        <Table.ColumnHeader w="30%">index</Table.ColumnHeader>
                        <Table.ColumnHeader w="55%">image</Table.ColumnHeader>
                        <Table.ColumnHeader w="10%">Actions</Table.ColumnHeader>
                    </Table.Row>
                </Table.Header>
                <Table.Body>
                    {items?.map((item) => (
                        <Table.Row key={item.id} opacity={isPlaceholderData ? 0.5 : 1}>
                            <Table.Cell truncate maxW="30%">
                                {item.id}
                            </Table.Cell>
                            <Table.Cell truncate maxW="30%">
                                {item.index}
                            </Table.Cell>
                            <Table.Cell
                                color={!item.image ? "gray" : "inherit"}
                                truncate
                                maxW="30%"
                            >
                                <Image
                                    src={imageBaseUrl + item.image}
                                    width="100px"
                                />
                            </Table.Cell>
                            <Table.Cell width="10%">
                                <MaCourseActionsMenu maCourse={item} />
                            </Table.Cell>
                        </Table.Row>
                    ))}
                </Table.Body>
            </Table.Root>
            <Flex justifyContent="flex-end" mt={4}>
                <PaginationRoot
                    count={count}
                    pageSize={PER_PAGE}
                    onPageChange={({ page }) => setPage(page)}
                >
                    <Flex>
                        <PaginationPrevTrigger />
                        <PaginationItems />
                        <PaginationNextTrigger />
                    </Flex>
                </PaginationRoot>
            </Flex>
        </>
    )
}

function MaCourse() {
    return (
        <Container maxW="full">
            <Heading size="lg" pt={12}>
                Ma Course Images Management
            </Heading>
            <AddMaCourse />
            <ItemsTable />
        </Container>
    )
}
