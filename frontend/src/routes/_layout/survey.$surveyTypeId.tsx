import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>lex,
  <PERSON>ing,
  Table,
  Text,
  IconButton,
  Badge,
  VStack,
} from "@chakra-ui/react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import { useState } from "react"
import { FiEdit, FiUpload } from "react-icons/fi"

import { QuestionnairesService, type QuestionWithDetails } from "@/client"
import ImportQuestionsModal from "@/components/Questionnaires/ImportQuestionsModal"
import EditQuestionModal from "@/components/Questionnaires/EditQuestionModal"

export const Route = createFileRoute("/_layout/survey/$surveyTypeId")({
  component: SurveyManagementPage,
})

function SurveyManagementPage() {
  const { surveyTypeId } = Route.useParams()
  const queryClient = useQueryClient()

  const [selectedQuestion, setSelectedQuestion] = useState<QuestionWithDetails | null>(null)
  const [isImportOpen, setIsImportOpen] = useState(false)
  const [isEditOpen, setIsEditOpen] = useState(false)

  const {
    data: surveyData,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["survey-questions", surveyTypeId],
    queryFn: () => QuestionnairesService.questionnairesGetSurveyQuestions({
      surveyTypeId: parseInt(surveyTypeId)
    }),
  })

  // Get survey types to show the survey name
  const { data: surveyTypes } = useQuery({
    queryKey: ["survey-types"],
    queryFn: () => QuestionnairesService.questionnairesGetSurveyTypes(),
  })

  const currentSurvey = surveyTypes?.find(
    (survey) => survey.survey_type_id === parseInt(surveyTypeId)
  )

  const handleEditQuestion = (question: QuestionWithDetails) => {
    setSelectedQuestion(question)
    setIsEditOpen(true)
  }

  const handleImportSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["survey-questions", surveyTypeId] })
    setIsImportOpen(false)
  }

  const handleEditSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["survey-questions", surveyTypeId] })
    setIsEditOpen(false)
    setSelectedQuestion(null)
  }

  if (isLoading) {
    return (
      <Container maxW="7xl" py={6}>
        <Text>Loading survey questions...</Text>
      </Container>
    )
  }

  if (isError) {
    return (
      <Container maxW="7xl" py={6}>
        <Text color="red.500">
          Error loading survey questions: {error?.message || "Unknown error"}
        </Text>
      </Container>
    )
  }

  return (
    <Container maxW="7xl" py={6}>
      <VStack gap={6} align="stretch">
        <Flex justify="space-between" align="center">
          <Box>
            <Heading size="lg">
              {currentSurvey?.name || `Survey ${surveyTypeId}`}
            </Heading>
            <Text color="gray.600" mt={2}>
              Manage questions for this survey
            </Text>
          </Box>
          <Button
            colorPalette="blue"
            onClick={() => setIsImportOpen(true)}
          >
            <FiUpload />
            Import Questions
            Import CSV
          </Button>
        </Flex>

        <Box
          bg={{ base: "white", _dark: "gray.800" }}
          borderRadius="lg"
          border="1px"
          borderColor={{ base: "gray.200", _dark: "gray.600" }}
          overflow="hidden"
        >
          <Box p={4} borderBottom="1px" borderColor={{ base: "gray.200", _dark: "gray.600" }}>
            <Flex justify="space-between" align="center">
              <Text fontWeight="semibold">
                Questions ({surveyData?.count || 0})
              </Text>
              <Badge colorPalette="blue" variant="subtle">
                Total: {surveyData?.count || 0}
              </Badge>
            </Flex>
          </Box>

          {surveyData?.data && surveyData.data.length > 0 ? (
            <Table.Root variant="outline">
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeader>Index</Table.ColumnHeader>
                  <Table.ColumnHeader>Intelligence Category</Table.ColumnHeader>
                  <Table.ColumnHeader>Core Competency</Table.ColumnHeader>
                  <Table.ColumnHeader>Question</Table.ColumnHeader>
                  <Table.ColumnHeader width="100px">Actions</Table.ColumnHeader>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {surveyData.data.map((question) => (
                  <Table.Row key={question.question_id}>
                    <Table.Cell>
                      <Badge variant="outline">{question.index}</Badge>
                    </Table.Cell>
                    <Table.Cell>{question.intelligence_category}</Table.Cell>
                    <Table.Cell>{question.core_competency}</Table.Cell>
                    <Table.Cell maxW="400px">
                      <Text lineClamp={2}>{question.content}</Text>
                    </Table.Cell>
                    <Table.Cell>
                      <IconButton
                        aria-label="Edit question"
                        size="sm"
                        variant="ghost"
                        colorPalette="blue"
                        onClick={() => handleEditQuestion(question)}
                      >
                        <FiEdit />
                      </IconButton>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table.Root>
          ) : (
            <Box p={8} textAlign="center">
              <Text color="gray.500" mb={4}>
                No questions found for this survey.
              </Text>
              <Button
                colorPalette="blue"
                onClick={() => setIsImportOpen(true)}
              >
                <FiUpload />
                Import Questions
                Import Questions from CSV
              </Button>
            </Box>
          )}
        </Box>
      </VStack>

      {/* Import Modal */}
      <ImportQuestionsModal
        open={isImportOpen}
        onOpenChange={setIsImportOpen}
        surveyTypeId={parseInt(surveyTypeId)}
        surveyName={currentSurvey?.name || `Survey ${surveyTypeId}`}
        onSuccess={handleImportSuccess}
      />

      {/* Edit Modal */}
      {selectedQuestion && (
        <EditQuestionModal
          open={isEditOpen}
          onOpenChange={setIsEditOpen}
          question={selectedQuestion}
          onSuccess={handleEditSuccess}
        />
      )}
    </Container>
  )
}

export default SurveyManagementPage
