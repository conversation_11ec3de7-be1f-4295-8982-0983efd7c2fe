import {
  Box,
  Container,
  Flex,
  <PERSON><PERSON>,
  <PERSON>,
  Badge,
  Button,
  VStack,
  HStack,
  Stack,
  IconButton,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useParams, useNavigate } from "@tanstack/react-router"
import { FiArrowLeft, FiPlus } from "react-icons/fi"
import { useState } from "react"

import { AdminService, QuestionnairesService } from "@/client"
import ClientActionsMenu from "@/components/Clients/ClientActionsMenu"
import QuestionnairesTable from "@/components/Questionnaires/QuestionnairesTable"
import AddQuestionnaireModal from "@/components/Questionnaires/AddQuestionnaireModal"

export const Route = createFileRoute("/_layout/client/$clientId")({
  component: ClientDetailsPage,
})

function getClientQueryOptions(clientId: string) {
  return {
    queryKey: ["client", clientId],
    queryFn: () => AdminService.adminReadClientAdmin({ clientId }),
  }
}

function getClientQuestionnairesQueryOptions(clientId: string) {
  return {
    queryKey: ["client-questionnaires", clientId],
    queryFn: () => QuestionnairesService.questionnairesGetClientQuestionnaires({ clientId }),
  }
}

function ClientDetailsPage() {
  const { clientId } = useParams({ strict: false })
  const navigate = useNavigate()
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const onAddModalOpen = () => setIsAddModalOpen(true)
  const onAddModalClose = () => setIsAddModalOpen(false)

  const { data: client, isLoading, error } = useQuery(
    getClientQueryOptions(clientId)
  )

  const { data: questionnairesData, isLoading: questionnairesLoading, refetch: refetchQuestionnaires } = useQuery({
    ...getClientQuestionnairesQueryOptions(clientId),
    enabled: !!clientId,
  })

  const handleQuestionnaireSuccess = async () => {
    console.log("handleQuestionnaireSuccess called, refetching questionnaires...")
    // Force an immediate refetch when questionnaire is created successfully
    const result = await refetchQuestionnaires()
    console.log("Refetch result:", result)
  }

  const handleBackToClients = () => {
    navigate({ to: "/clients" })
  }

  // Check if client has an available questionnaire (to disable add button)
  const hasAvailableQuestionnaire = questionnairesData?.data?.some(
    (q) => q.status === "available"
  )

  if (isLoading) {
    return <ClientDetailsSkeleton />
  }

  if (error || !client) {
    return (
      <Container maxW="full">
        <VStack gap={6} py={8}>
          <Text fontSize="lg" color="red.500">
            {error ? "Error loading client details" : "Client not found"}
          </Text>
          <Button onClick={handleBackToClients}>
            <FiArrowLeft />
            Back to Clients
          </Button>
        </VStack>
      </Container>
    )
  }

  return (
    <Container maxW="full">
      <VStack gap={6} py={8} align="stretch">
        {/* Header */}
        <Flex justify="space-between" align="center">
          <HStack gap={4}>
            <IconButton
              aria-label="Back to clients"
              onClick={handleBackToClients}
              variant="outline"
              size="sm"
            >
              <FiArrowLeft />
            </IconButton>
            <Heading size="lg">Client Details</Heading>
          </HStack>
          <ClientActionsMenu client={client} />
        </Flex>

        {/* Client Information Card */}
        <Box
          bg="white"
          p={6}
          borderRadius="lg"
          shadow="sm"
          border="1px"
          borderColor="gray.200"
        >
          <VStack gap={6} align="stretch">
            {/* Basic Info Section */}
            <Box>
              <Heading size="md" mb={4} color="gray.700">
                Basic Information
              </Heading>
              <Stack gap={4}>
                <Flex justify="space-between" align="center">
                  <Text fontWeight="medium" color="gray.600">
                    Full Name:
                  </Text>
                  <Text fontSize="lg" fontWeight="semibold">
                    {client.full_name || "Not provided"}
                  </Text>
                </Flex>
                
                <Flex justify="space-between" align="center">
                  <Text fontWeight="medium" color="gray.600">
                    Email:
                  </Text>
                  <Text fontSize="lg">{client.email}</Text>
                </Flex>
                
                <Flex justify="space-between" align="center">
                  <Text fontWeight="medium" color="gray.600">
                    Phone Number:
                  </Text>
                  <Text fontSize="lg">
                    {client.phone_number || "Not provided"}
                  </Text>
                </Flex>
                
                <Flex justify="space-between" align="center">
                  <Text fontWeight="medium" color="gray.600">
                    Status:
                  </Text>
                  <Badge
                    colorScheme={client.is_active ? "green" : "red"}
                    size="lg"
                    px={3}
                    py={1}
                  >
                    {client.is_active ? "Active" : "Inactive"}
                  </Badge>
                </Flex>
                
                <Flex justify="space-between" align="center">
                  <Text fontWeight="medium" color="gray.600">
                    Client ID:
                  </Text>
                  <Text fontSize="sm" fontFamily="mono" color="gray.500">
                    {client.id}
                  </Text>
                </Flex>
                
                {client.created_at && (
                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Created:
                    </Text>
                    <Text fontSize="lg">
                      {new Date(client.created_at).toLocaleDateString()}
                    </Text>
                  </Flex>
                )}
              </Stack>
            </Box>
          </VStack>
        </Box>

        {/* Questionnaires Section */}
        <Box
          bg="white"
          p={6}
          borderRadius="lg"
          shadow="sm"
          border="1px"
          borderColor="gray.200"
        >
          <VStack gap={6} align="stretch">
            <Flex justify="space-between" align="center">
              <Heading size="md" color="gray.700">
                Questionnaires
              </Heading>
              <Button
                colorScheme="blue"
                size="sm"
                onClick={onAddModalOpen}
                disabled={hasAvailableQuestionnaire}
              >
                <FiPlus />
                Add Questionnaire
              </Button>
            </Flex>

            {questionnairesLoading ? (
              <Text color="gray.500" textAlign="center" py={8}>
                Loading questionnaires...
              </Text>
            ) : (
              <QuestionnairesTable
                questionnaires={questionnairesData?.data || []}
                clientId={clientId}
              />
            )}
          </VStack>
        </Box>

        {/* Add Questionnaire Modal */}
        <AddQuestionnaireModal
          isOpen={isAddModalOpen}
          onClose={onAddModalClose}
          clientId={clientId}
          onSuccess={handleQuestionnaireSuccess}
        />
      </VStack>
    </Container>
  )
}

function ClientDetailsSkeleton() {
  return (
    <Container maxW="full">
      <VStack gap={6} py={8} align="stretch">
        {/* Header Skeleton */}
        <Flex justify="space-between" align="center">
          <HStack gap={4}>
            <Box w={10} h={10} bg="gray.200" borderRadius="md" />
            <Box w={40} h={8} bg="gray.200" borderRadius="md" />
          </HStack>
          <Box w={10} h={10} bg="gray.200" borderRadius="md" />
        </Flex>

        {/* Content Skeleton */}
        <Box
          bg="white"
          p={6}
          borderRadius="lg"
          shadow="sm"
          border="1px"
          borderColor="gray.200"
        >
          <VStack gap={6} align="stretch">
            <Box w={48} h={6} bg="gray.200" borderRadius="md" />
            <Stack gap={4}>
              {Array.from({ length: 6 }).map((_, i) => (
                <Flex key={i} justify="space-between" align="center">
                  <Box w={24} h={5} bg="gray.200" borderRadius="md" />
                  <Box w={32} h={5} bg="gray.200" borderRadius="md" />
                </Flex>
              ))}
            </Stack>
          </VStack>
        </Box>
      </VStack>
    </Container>
  )
}
