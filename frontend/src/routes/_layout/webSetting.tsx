import {
    Button,
    Input,
    Heading,
    VStack,
    Box,
    Container,
} from "@chakra-ui/react"
import { useQueryClient, useMutation } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import useCustomToast from "../../hooks/useCustomToast"
import { SettingService, type SettingBase, type ApiError, } from "../../client"
import { handleError } from "../../utils"
import { type SubmitHandler, useForm } from "react-hook-form"
import { Field } from "../../components/ui/field"
export const Route = createFileRoute("/_layout/webSetting")({
    component: WebSetting,
})

function WebSettingForms() {
    const { showSuccessToast } = useCustomToast()
    const queryClient = useQueryClient()

    const {
        register,
        handleSubmit,

        formState: { errors, isValid, isSubmitting },
    } = useForm<SettingBase>({
        mode: "onBlur",
        criteriaMode: "all",
        defaultValues: async () => SettingService.settingReadSetting()
    })

    const mutation = useMutation({
        mutationFn: (data: SettingBase) =>
            SettingService.settingUpdateSetting({ requestBody: data }),
        onSuccess: () => {
            showSuccessToast("Settings updated successfully.")
        },
        onError: (err: ApiError) => {
            handleError(err)
        },
        onSettled: () => {
            queryClient.invalidateQueries({ queryKey: ["webSetting"] })
        },
    })

    const onSubmit: SubmitHandler<SettingBase> = async (data) => {
        mutation.mutate(data)
    }

    return (
        <Box as="form" onSubmit={handleSubmit(onSubmit)} w="full" mt={10}>
            <VStack align="stretch">
                <Field
                    required
                    invalid={!!errors.address}
                    errorText={errors.address?.message}
                    label="Address"
                >
                    <Input
                        id="address"
                        type="text"
                        {...register("address", {
                            required: "address is required",
                        })}
                    />
                </Field>

                <Field
                    required
                    invalid={!!errors.latitude}
                    errorText={errors.latitude?.message}
                    label="Latitude"
                >
                    <Input
                        id="latitude"
                        type="number"
                        step="any"
                        {...register("latitude", {
                            required: "latitude is required",
                        })}
                    />
                </Field>

                <Field
                    required
                    invalid={!!errors.longitude}
                    errorText={errors.longitude?.message}
                    label="Longitude"
                >
                    <Input
                        id="longitude"
                        type="number"
                        step="any"
                        {...register("longitude", {
                            required: "longitude is required",
                        })}
                    />

                </Field>
                <Field
                    required
                    invalid={!!errors.phone}
                    errorText={errors.phone?.message}
                    label="Phone Number"
                >
                    <Input
                        id="phone"
                        type="text"
                        {...register("phone", {
                            required: "phone is required",
                        })}
                    />
                </Field>
                <Field
                    required
                    invalid={!!errors.phone}
                    errorText={errors.phone?.message}
                    label="Email"
                >
                    <Input
                        id="email"
                        type="email"
                        {...register("email", {
                            required: "email is required",
                        })}
                    />
                </Field>
                <Field
                    required
                    invalid={!!errors.facebook}
                    errorText={errors.facebook?.message}
                    label="Facebook Link"
                >
                    <Input
                        id="facebook"
                        type="text"
                        {...register("facebook", {
                            required: "facebook is required",
                        })}
                    />
                </Field>

                <Field
                    required
                    invalid={!!errors.whatsapp}
                    errorText={errors.whatsapp?.message}
                    label="Whatsapp Link"
                >
                    <Input
                        id="whatsapp"
                        type="text"
                        {...register("whatsapp", {
                            required: "whatsapp is required",
                        })}
                    />
                </Field>
                <Button
                    type="submit"
                    colorPalette="blue"
                    variant="solid"
                    disabled={!isValid || isSubmitting}
                    loading={isSubmitting || mutation.isPending}
                    mt={4}
                >
                    Save
                </Button>
            </VStack>
        </Box>
    )
}

function WebSetting() {
    return (
        <Container maxW="container.md" py={10}>
            <Heading mb={6}>Website Settings</Heading>
            <WebSettingForms />
        </Container>
    )
}