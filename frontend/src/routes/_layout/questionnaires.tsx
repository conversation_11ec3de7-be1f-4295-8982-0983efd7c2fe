import {
  Box,
  Container,
  Heading,
  VStack,
  Text,
  Accordion,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { Link as RouterLink } from "@tanstack/react-router"
import { createFileRoute } from "@tanstack/react-router"

import { QuestionnairesService } from "@/client"

export const Route = createFileRoute("/_layout/questionnaires")({
  component: QuestionnairesPage,
})

function QuestionnairesPage() {

  const {
    data: surveyTypes,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["survey-types"],
    queryFn: () => QuestionnairesService.questionnairesGetSurveyTypes(),
  })

  if (isLoading) {
    return (
      <Container maxW="7xl" py={6}>
        <Text>Loading survey types...</Text>
      </Container>
    )
  }

  if (isError) {
    return (
      <Container maxW="7xl" py={6}>
        <Text color="red.500">
          Error loading survey types: {error?.message || "Unknown error"}
        </Text>
      </Container>
    )
  }

  return (
    <Container maxW="7xl" py={6}>
      <VStack gap={6} align="stretch">
        <Heading size="lg" textAlign="center">
          Questionnaires Management
        </Heading>

        <Box
          bg={{ base: "white", _dark: "gray.800" }}
          p={6}
          borderRadius="lg"
          border="1px"
          borderColor={{ base: "gray.200", _dark: "gray.600" }}
          shadow="sm"
        >
          <Accordion.Root collapsible>
            <Accordion.Item value="questionnaires">
              <Accordion.ItemTrigger
                _hover={{ bg: "gray.50" }}
                _expanded={{ bg: "gray.100" }}
                borderRadius="md"
                p={4}
              >
                <Box flex="1" textAlign="left">
                  <Text fontSize="lg" fontWeight="semibold">
                    📁 Questionnaires
                  </Text>
                  <Text fontSize="sm" color="gray.600" mt={1}>
                    Manage age-specific survey questionnaires
                  </Text>
                </Box>
                <Accordion.ItemIndicator />
              </Accordion.ItemTrigger>

              <Accordion.ItemContent>
                <Accordion.ItemBody pb={4} pl={8}>
                  <VStack gap={3} align="stretch">
                    {surveyTypes?.map((surveyType) => (
                      <RouterLink
                        key={surveyType.survey_type_id}
                        to={`/survey/${surveyType.survey_type_id}`}
                        style={{ textDecoration: "none" }}
                      >
                        <Box
                          p={3}
                          borderRadius="md"
                          border="1px"
                          borderColor="gray.200"
                          _hover={{
                            bg: "blue.50",
                            borderColor: "blue.300",
                            transform: "translateY(-1px)",
                          }}
                          transition="all 0.2s"
                          cursor="pointer"
                        >
                          <Text fontWeight="medium" color="blue.600">
                            📋 {surveyType.name}
                          </Text>
                          <Text fontSize="sm" color="gray.500" mt={1}>
                            Manage questions for {surveyType.name.toLowerCase()}
                          </Text>
                        </Box>
                      </RouterLink>
                    ))}
                  </VStack>
                </Accordion.ItemBody>
              </Accordion.ItemContent>
            </Accordion.Item>
          </Accordion.Root>
        </Box>

        <Box
          bg="blue.50"
          p={4}
          borderRadius="md"
          border="1px"
          borderColor="blue.200"
        >
          <Text fontSize="sm" color="blue.700">
            <strong>Instructions:</strong>
            <br />
            • Click on "Questionnaires" folder to expand and see available surveys
            <br />
            • Click on any survey to manage its questions
            <br />
            • Use the Import button to upload CSV files with questions
            <br />
            • Use the Edit button to modify individual questions
          </Text>
        </Box>
      </VStack>
    </Container>
  )
}

export default QuestionnairesPage
