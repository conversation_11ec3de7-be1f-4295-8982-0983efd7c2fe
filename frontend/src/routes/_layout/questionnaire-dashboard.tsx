import {
  <PERSON>,
  But<PERSON>,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Table,
  Badge,
  Card,
  Flex,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, Link } from "@tanstack/react-router"

import { QuestionnairesService } from "@/client"

export const Route = createFileRoute("/_layout/questionnaire-dashboard")({
  component: QuestionnaireDashboardPage,
})

// Mock dashboard data
const mockDashboardData = {
  totalQuestionnaires: 156,
  completedToday: 12,
  pendingReview: 8,
  averageCompletionTime: "25分鐘",
  recentCompletions: [
    {
      id: "1",
      clientName: "小明",
      surveyType: "3-6歲評估",
      completedAt: "2025-01-12 14:30",
      status: "已完成",
      score: 85
    },
    {
      id: "2", 
      clientName: "小華",
      surveyType: "7-11歲評估",
      completedAt: "2025-01-12 13:15",
      status: "已完成",
      score: 78
    },
    {
      id: "3",
      clientName: "小美",
      surveyType: "12-15歲評估", 
      completedAt: "2025-01-12 11:45",
      status: "已完成",
      score: 92
    },
    {
      id: "4",
      clientName: "小強",
      surveyType: "3-6歲評估",
      completedAt: "2025-01-12 10:20",
      status: "進行中",
      score: null
    },
    {
      id: "5",
      clientName: "小玲",
      surveyType: "7-11歲評估",
      completedAt: "2025-01-12 09:30",
      status: "已完成",
      score: 88
    }
  ]
}

function QuestionnaireDashboardPage() {
  // In real app, fetch dashboard data from API
  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ["questionnaire-dashboard"],
    queryFn: () => Promise.resolve(mockDashboardData), // Mock data for now
  })

  if (isLoading) {
    return (
      <Container maxW="7xl" py={8}>
        <Text>載入中...</Text>
      </Container>
    )
  }

  const data = dashboardData || mockDashboardData

  return (
    <Container maxW="7xl" py={8}>
      <VStack gap={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="xl" mb={2}>
            問卷管理儀表板
          </Heading>
          <Text color="gray.600" fontSize="lg">
            智能核心評估系統概覽
          </Text>
        </Box>

        {/* Statistics Cards */}
        <Flex gap={6} wrap="wrap">
          <Card.Root flex="1" minW="200px">
            <Card.Body>
              <VStack align="start">
                <Text fontSize="sm" color="gray.600">
                  總問卷數量
                </Text>
                <Text fontSize="3xl" fontWeight="bold" color="blue.500">
                  {data.totalQuestionnaires}
                </Text>
              </VStack>
            </Card.Body>
          </Card.Root>

          <Card.Root flex="1" minW="200px">
            <Card.Body>
              <VStack align="start">
                <Text fontSize="sm" color="gray.600">
                  今日完成
                </Text>
                <Text fontSize="3xl" fontWeight="bold" color="green.500">
                  {data.completedToday}
                </Text>
              </VStack>
            </Card.Body>
          </Card.Root>

          <Card.Root flex="1" minW="200px">
            <Card.Body>
              <VStack align="start">
                <Text fontSize="sm" color="gray.600">
                  待審核
                </Text>
                <Text fontSize="3xl" fontWeight="bold" color="orange.500">
                  {data.pendingReview}
                </Text>
              </VStack>
            </Card.Body>
          </Card.Root>

          <Card.Root flex="1" minW="200px">
            <Card.Body>
              <VStack align="start">
                <Text fontSize="sm" color="gray.600">
                  平均完成時間
                </Text>
                <Text fontSize="3xl" fontWeight="bold" color="purple.500">
                  {data.averageCompletionTime}
                </Text>
              </VStack>
            </Card.Body>
          </Card.Root>
        </Flex>

        {/* Recent Completions Table */}
        <Card.Root>
          <Card.Header>
            <Heading size="md">最近完成的問卷</Heading>
          </Card.Header>
          <Card.Body>
            <Table.Root>
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeader>客戶姓名</Table.ColumnHeader>
                  <Table.ColumnHeader>評估類型</Table.ColumnHeader>
                  <Table.ColumnHeader>完成時間</Table.ColumnHeader>
                  <Table.ColumnHeader>狀態</Table.ColumnHeader>
                  <Table.ColumnHeader>分數</Table.ColumnHeader>
                  <Table.ColumnHeader>操作</Table.ColumnHeader>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {data.recentCompletions.map((completion) => (
                  <Table.Row key={completion.id}>
                    <Table.Cell fontWeight="medium">
                      {completion.clientName}
                    </Table.Cell>
                    <Table.Cell>{completion.surveyType}</Table.Cell>
                    <Table.Cell>{completion.completedAt}</Table.Cell>
                    <Table.Cell>
                      <Badge
                        colorScheme={completion.status === "已完成" ? "green" : "yellow"}
                      >
                        {completion.status}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      {completion.score ? (
                        <Text fontWeight="bold" color="blue.600">
                          {completion.score}分
                        </Text>
                      ) : (
                        <Text color="gray.400">-</Text>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <HStack gap={2}>
                        {completion.status === "已完成" && (
                          <Button size="sm" variant="outline">
                            查看報告
                          </Button>
                        )}
                        <Button size="sm" variant="ghost">
                          詳情
                        </Button>
                      </HStack>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table.Root>
          </Card.Body>
        </Card.Root>

        {/* Quick Actions */}
        <Card.Root>
          <Card.Header>
            <Heading size="md">快速操作</Heading>
          </Card.Header>
          <Card.Body>
            <HStack gap={4} wrap="wrap">
              <Link to="/clients">
                <Button colorScheme="blue">
                  管理客戶
                </Button>
              </Link>
              <Button colorScheme="green">
                匯出報告
              </Button>
              <Button colorScheme="purple">
                系統設定
              </Button>
              <Button variant="outline">
                查看統計
              </Button>
            </HStack>
          </Card.Body>
        </Card.Root>
      </VStack>
    </Container>
  )
}
