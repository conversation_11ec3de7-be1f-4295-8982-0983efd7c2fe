import { <PERSON><PERSON>, Con<PERSON>er, <PERSON><PERSON>, <PERSON><PERSON>, Table, Button, EmptyState, VStack, NativeSelect, Link } from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useNavigate, Link as RouterLink } from "@tanstack/react-router"
import { z } from "zod"
import { FiArrowUp, FiArrowDown, FiUsers } from "react-icons/fi"

import AddClient from "@/components/Clients/AddClient"
import ClientActionsMenu from "@/components/Clients/ClientActionsMenu"
import ClientsTableSkeleton from "@/components/Clients/ClientsTableSkeleton"
import { AdminService, type ClientsPublic } from "@/client"
import {
  PaginationItems,
  PaginationNextTrigger,
  PaginationPrevTrigger,
  PaginationRoot,
} from "@/components/ui/pagination.tsx"

// Using types from AdminService

const clientsSearchSchema = z.object({
  page: z.number().catch(1),
  sort_by: z.string().catch("created_at"),
  sort_order: z.string().catch("desc"),
  status_filter: z.string().catch("all"),
})

const PER_PAGE = 20

function getClientsQueryOptions({ 
  page, 
  sort_by, 
  sort_order, 
  status_filter 
}: { 
  page: number
  sort_by: string
  sort_order: string
  status_filter: string
}) {
  return {
    queryFn: (): Promise<ClientsPublic> =>
      AdminService.adminReadClients({
        skip: (page - 1) * PER_PAGE,
        limit: PER_PAGE,
        sortBy: sort_by,
        sortOrder: sort_order,
        statusFilter: status_filter,
      }),
    queryKey: ["clients", { page, sort_by, sort_order, status_filter }],
  }
}

export const Route = createFileRoute("/_layout/clients")({
  component: Clients,
  validateSearch: (search) => clientsSearchSchema.parse(search),
})

function ClientsTable() {
  const navigate = useNavigate({ from: Route.fullPath })
  const { page, sort_by, sort_order, status_filter } = Route.useSearch()

  const { data, isLoading, isPlaceholderData, error } = useQuery({
    ...getClientsQueryOptions({ page, sort_by, sort_order, status_filter }),
    placeholderData: (prevData) => prevData,
  })

  const setPage = (page: number) =>
    navigate({
      search: (prev: any) => ({ ...prev, page }),
    })

  const setSorting = (field: string) => {
    const newOrder = sort_by === field && sort_order === "desc" ? "asc" : "desc"
    navigate({
      search: (prev: any) => ({ ...prev, sort_by: field, sort_order: newOrder, page: 1 }),
    })
  }

  const setStatusFilter = (filter: string) => {
    navigate({
      search: (prev: any) => ({ ...prev, status_filter: filter, page: 1 }),
    })
  }

  const clients = data?.data ?? []
  const count = data?.count ?? 0

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getSortIcon = (field: string) => {
    if (sort_by !== field) return null
    return sort_order === "desc" ? <FiArrowDown /> : <FiArrowUp />
  }

  if (isLoading) {
    return (
      <>
        {/* Filters */}
        <Flex mb={4} gap={4} align="center">
          <NativeSelect.Root width="200px" disabled>
            <NativeSelect.Field
              value={status_filter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </NativeSelect.Field>
            <NativeSelect.Indicator />
          </NativeSelect.Root>
        </Flex>
        <ClientsTableSkeleton />
      </>
    )
  }

  if (error) {
    return (
      <EmptyState.Root>
        <EmptyState.Content>
          <EmptyState.Indicator>
            <FiUsers />
          </EmptyState.Indicator>
          <VStack textAlign="center">
            <EmptyState.Title>Failed to load clients</EmptyState.Title>
            <EmptyState.Description>
              {error instanceof Error ? error.message : "Something went wrong while loading clients."}
            </EmptyState.Description>
          </VStack>
        </EmptyState.Content>
      </EmptyState.Root>
    )
  }

  if (clients.length === 0) {
    return (
      <>
        {/* Filters */}
        <Flex mb={4} gap={4} align="center">
          <NativeSelect.Root width="200px">
            <NativeSelect.Field
              value={status_filter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </NativeSelect.Field>
            <NativeSelect.Indicator />
          </NativeSelect.Root>
        </Flex>
        <EmptyState.Root>
          <EmptyState.Content>
            <EmptyState.Indicator>
              <FiUsers />
            </EmptyState.Indicator>
            <VStack textAlign="center">
              <EmptyState.Title>
                {status_filter === "all" ? "No clients found" : `No ${status_filter} clients found`}
              </EmptyState.Title>
              <EmptyState.Description>
                {status_filter === "all"
                  ? "Add a new client to get started"
                  : `Try changing the filter or add a new client`
                }
              </EmptyState.Description>
            </VStack>
          </EmptyState.Content>
        </EmptyState.Root>
      </>
    )
  }

  return (
    <>
      {/* Filters */}
      <Flex mb={4} gap={4} align="center">
        <NativeSelect.Root width="200px">
          <NativeSelect.Field
            value={status_filter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </NativeSelect.Field>
          <NativeSelect.Indicator />
        </NativeSelect.Root>
      </Flex>

      <Table.Root size={{ base: "sm", md: "md" }}>
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader w="20%">
              <Button
                variant="ghost"
                onClick={() => setSorting("created_at")}
              >
                Created At {getSortIcon("created_at")}
              </Button>
            </Table.ColumnHeader>
            <Table.ColumnHeader w="25%">Name</Table.ColumnHeader>
            <Table.ColumnHeader w="25%">Email</Table.ColumnHeader>
            <Table.ColumnHeader w="15%">Phone</Table.ColumnHeader>
            <Table.ColumnHeader w="10%">Status</Table.ColumnHeader>
            <Table.ColumnHeader w="5%">Actions</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {clients?.map((client) => (
            <Table.Row key={client.id} opacity={isPlaceholderData ? 0.5 : 1}>
              <Table.Cell w="20%">
                {client.created_at ? formatDate(client.created_at) : "N/A"}
              </Table.Cell>
              <Table.Cell w="25%" color={!client.full_name ? "gray" : "inherit"}>
                <Link asChild>
                  <RouterLink
                    to="/client/$clientId"
                    params={{ clientId: client.id }}
                    style={{
                      textDecoration: "none",
                      color: "inherit",
                      fontWeight: client.full_name ? "medium" : "normal",
                    }}
                    _hover={{
                      textDecoration: "underline",
                      color: "blue.600",
                    }}
                  >
                    {client.full_name || "N/A"}
                  </RouterLink>
                </Link>
              </Table.Cell>
              <Table.Cell w="25%">{client.email}</Table.Cell>
              <Table.Cell w="15%" color={!client.phone_number ? "gray" : "inherit"}>
                {client.phone_number || "N/A"}
              </Table.Cell>
              <Table.Cell w="10%">
                <Badge colorPalette={client.is_active ? "green" : "red"}>
                  {client.is_active ? "Active" : "Inactive"}
                </Badge>
              </Table.Cell>
              <Table.Cell w="5%">
                <ClientActionsMenu client={client} />
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
      <Flex justifyContent="flex-end" mt={4}>
        <PaginationRoot
          count={count}
          pageSize={PER_PAGE}
          page={page}
          onPageChange={({ page }) => setPage(page)}
        >
          <Flex>
            <PaginationPrevTrigger />
            <PaginationItems />
            <PaginationNextTrigger />
          </Flex>
        </PaginationRoot>
      </Flex>
    </>
  )
}

function Clients() {
  return (
    <Container maxW="full">
      <Heading size="lg" pt={12}>
        Clients Management
      </Heading>

      <AddClient />
      <ClientsTable />
    </Container>
  )
}
