/* prettier-ignore-start */

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file is auto-generated by TanStack Router

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as SignupImport } from './routes/signup'
import { Route as ResetPasswordImport } from './routes/reset-password'
import { Route as RecoverPasswordImport } from './routes/recover-password'
import { Route as LoginImport } from './routes/login'
import { Route as LayoutImport } from './routes/_layout'
import { Route as LayoutIndexImport } from './routes/_layout/index'
import { Route as LayoutWebSettingImport } from './routes/_layout/webSetting'
import { Route as LayoutSettingsImport } from './routes/_layout/settings'
import { Route as LayoutQuestionnairesImport } from './routes/_layout/questionnaires'
import { Route as LayoutQuestionnaireDashboardImport } from './routes/_layout/questionnaire-dashboard'
import { Route as LayoutMaCourseImport } from './routes/_layout/maCourse'
import { Route as LayoutMaImport } from './routes/_layout/ma'
import { Route as LayoutItemsImport } from './routes/_layout/items'
import { Route as LayoutIntroductionsImport } from './routes/_layout/introductions'
import { Route as LayoutClientsImport } from './routes/_layout/clients'
import { Route as LayoutAdminImport } from './routes/_layout/admin'
import { Route as LayoutActivitysImport } from './routes/_layout/activitys'
import { Route as LayoutAboutusImport } from './routes/_layout/aboutus'
import { Route as LayoutSurveySurveyTypeIdImport } from './routes/_layout/survey.$surveyTypeId'
import { Route as LayoutClientClientIdImport } from './routes/_layout/client.$clientId'

// Create/Update Routes

const SignupRoute = SignupImport.update({
  path: '/signup',
  getParentRoute: () => rootRoute,
} as any)

const ResetPasswordRoute = ResetPasswordImport.update({
  path: '/reset-password',
  getParentRoute: () => rootRoute,
} as any)

const RecoverPasswordRoute = RecoverPasswordImport.update({
  path: '/recover-password',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const LayoutRoute = LayoutImport.update({
  id: '/_layout',
  getParentRoute: () => rootRoute,
} as any)

const LayoutIndexRoute = LayoutIndexImport.update({
  path: '/',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutWebSettingRoute = LayoutWebSettingImport.update({
  path: '/webSetting',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutSettingsRoute = LayoutSettingsImport.update({
  path: '/settings',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutQuestionnairesRoute = LayoutQuestionnairesImport.update({
  path: '/questionnaires',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutQuestionnaireDashboardRoute =
  LayoutQuestionnaireDashboardImport.update({
    path: '/questionnaire-dashboard',
    getParentRoute: () => LayoutRoute,
  } as any)

const LayoutMaCourseRoute = LayoutMaCourseImport.update({
  path: '/maCourse',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutMaRoute = LayoutMaImport.update({
  path: '/ma',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutItemsRoute = LayoutItemsImport.update({
  path: '/items',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutIntroductionsRoute = LayoutIntroductionsImport.update({
  path: '/introductions',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutClientsRoute = LayoutClientsImport.update({
  path: '/clients',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutAdminRoute = LayoutAdminImport.update({
  path: '/admin',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutActivitysRoute = LayoutActivitysImport.update({
  path: '/activitys',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutAboutusRoute = LayoutAboutusImport.update({
  path: '/aboutus',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutSurveySurveyTypeIdRoute = LayoutSurveySurveyTypeIdImport.update({
  path: '/survey/$surveyTypeId',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutClientClientIdRoute = LayoutClientClientIdImport.update({
  path: '/client/$clientId',
  getParentRoute: () => LayoutRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_layout': {
      preLoaderRoute: typeof LayoutImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/recover-password': {
      preLoaderRoute: typeof RecoverPasswordImport
      parentRoute: typeof rootRoute
    }
    '/reset-password': {
      preLoaderRoute: typeof ResetPasswordImport
      parentRoute: typeof rootRoute
    }
    '/signup': {
      preLoaderRoute: typeof SignupImport
      parentRoute: typeof rootRoute
    }
    '/_layout/aboutus': {
      preLoaderRoute: typeof LayoutAboutusImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/activitys': {
      preLoaderRoute: typeof LayoutActivitysImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/admin': {
      preLoaderRoute: typeof LayoutAdminImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/clients': {
      preLoaderRoute: typeof LayoutClientsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/introductions': {
      preLoaderRoute: typeof LayoutIntroductionsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/items': {
      preLoaderRoute: typeof LayoutItemsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/ma': {
      preLoaderRoute: typeof LayoutMaImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/maCourse': {
      preLoaderRoute: typeof LayoutMaCourseImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/questionnaire-dashboard': {
      preLoaderRoute: typeof LayoutQuestionnaireDashboardImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/questionnaires': {
      preLoaderRoute: typeof LayoutQuestionnairesImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/settings': {
      preLoaderRoute: typeof LayoutSettingsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/webSetting': {
      preLoaderRoute: typeof LayoutWebSettingImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/': {
      preLoaderRoute: typeof LayoutIndexImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/client/$clientId': {
      preLoaderRoute: typeof LayoutClientClientIdImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/survey/$surveyTypeId': {
      preLoaderRoute: typeof LayoutSurveySurveyTypeIdImport
      parentRoute: typeof LayoutImport
    }
  }
}

// Create and export the route tree

export const routeTree = rootRoute.addChildren([
  LayoutRoute.addChildren([
    LayoutAboutusRoute,
    LayoutActivitysRoute,
    LayoutAdminRoute,
    LayoutClientsRoute,
    LayoutIntroductionsRoute,
    LayoutItemsRoute,
    LayoutMaRoute,
    LayoutMaCourseRoute,
    LayoutQuestionnaireDashboardRoute,
    LayoutQuestionnairesRoute,
    LayoutSettingsRoute,
    LayoutWebSettingRoute,
    LayoutIndexRoute,
    LayoutClientClientIdRoute,
    LayoutSurveySurveyTypeIdRoute,
  ]),
  LoginRoute,
  RecoverPasswordRoute,
  ResetPasswordRoute,
  SignupRoute,
])

/* prettier-ignore-end */
