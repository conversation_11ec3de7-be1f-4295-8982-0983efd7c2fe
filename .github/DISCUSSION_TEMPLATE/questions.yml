labels: [question]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for your interest in this project! 🚀

        Please follow these instructions, fill every question, and do every step. 🙏

        I'm asking this because answering questions and solving problems in GitHub is what consumes most of the time.

        I end up not being able to add new features, fix bugs, review pull requests, etc. as fast as I wish because I have to spend too much time handling questions.

        All that, on top of all the incredible help provided by a bunch of community members, that give a lot of their time to come here and help others.

        That's a lot of work, but if more users came to help others like them just a little bit more, it would be much less effort for them (and you and me 😅).

        By asking questions in a structured way (following this) it will be much easier to help you.

        And there's a high chance that you will find the solution along the way and you won't even have to submit it and wait for an answer. 😎

        As there are too many questions, I'll have to discard and close the incomplete ones. That will allow me (and others) to focus on helping people like you that follow the whole process and help us help you. 🤓
  - type: checkboxes
    id: checks
    attributes:
      label: First Check
      description: Please confirm and check all the following options.
      options:
        - label: I added a very descriptive title here.
          required: true
        - label: I used the GitHub search to find a similar question and didn't find it.
          required: true
        - label: I searched in the documentation/README.
          required: true
        - label: I already searched in Google "How to do X" and didn't find any information.
          required: true
        - label: I already read and followed all the tutorial in the docs/README and didn't find an answer.
          required: true
  - type: checkboxes
    id: help
    attributes:
      label: Commit to Help
      description: |
        After submitting this, I commit to one of:

          * Read open questions until I find 2 where I can help someone and add a comment to help there.
          * I already hit the "watch" button in this repository to receive notifications and I commit to help at least 2 people that ask questions in the future.

      options:
        - label: I commit to help with one of those options 👆
          required: true
  - type: textarea
    id: example
    attributes:
      label: Example Code
      description: |
        Please add a self-contained, [minimal, reproducible, example](https://stackoverflow.com/help/minimal-reproducible-example) with your use case.

        If I (or someone) can copy it, run it, and see it right away, there's a much higher chance I (or someone) will be able to help you.

      placeholder: |
        Write your example code here.
      render: Text
    validations:
      required: true
  - type: textarea
    id: description
    attributes:
      label: Description
      description: |
        What is the problem, question, or error?

        Write a short description telling me what you are doing, what you expect to happen, and what is currently happening.
      placeholder: |
        * Open the browser and call the endpoint `/`.
        * It returns a JSON with `{"message": "Hello World"}`.
        * But I expected it to return `{"message": "Hello Morty"}`.
    validations:
      required: true
  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you on?
      multiple: true
      options:
        - Linux
        - Windows
        - macOS
        - Other
    validations:
      required: true
  - type: textarea
    id: os-details
    attributes:
      label: Operating System Details
      description: You can add more details about your operating system here, in particular if you chose "Other".
    validations:
      required: true
  - type: input
    id: python-version
    attributes:
      label: Python Version
      description: |
        What Python version are you using?

        You can find the Python version with:

        ```bash
        python --version
        ```
    validations:
      required: true
  - type: textarea
    id: context
    attributes:
      label: Additional Context
      description: Add any additional context information or screenshots you think are useful.
