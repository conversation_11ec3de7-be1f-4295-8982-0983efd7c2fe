name: Latest Changes

on:
  pull_request_target:
    branches:
      - master
    types:
      - closed
  workflow_dispatch:
    inputs:
      number:
        description: PR number
        required: true
      debug_enabled:
        description: "Run the build with tmate debugging enabled (https://github.com/marketplace/actions/debugging-with-tmate)"
        required: false
        default: "false"

jobs:
  latest-changes:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read
    steps:
      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"
      - uses: actions/checkout@v4
        with:
          # To allow latest-changes to commit to the main branch
          token: ${{ secrets.LATEST_CHANGES }}
      - uses: tiangolo/latest-changes@0.3.2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          latest_changes_file: ./release-notes.md
          latest_changes_header: "## Latest Changes"
          end_regex: "^## "
          debug_logs: true
          label_header_prefix: "### "
