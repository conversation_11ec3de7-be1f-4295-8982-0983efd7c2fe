{"name": "frontend_client_side", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate-client": "openapi-ts"}, "dependencies": {"@chakra-ui/modal": "^2.3.1", "@chakra-ui/react": "^3.14.2", "@emotion/react": "^11.14.0", "@hey-api/client-axios": "^0.6.3", "@hookform/resolvers": "^5.1.1", "@react-google-maps/api": "^2.20.6", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.69.0", "@tanstack/react-router": "^1.114.27", "@tanstack/router-devtools": "^1.114.27", "axios": "^1.8.4", "embla-carousel-react": "^8.5.2", "moment": "^2.30.1", "motion": "^12.6.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.21.0", "@hey-api/openapi-ts": "^0.64.15", "@tanstack/eslint-plugin-query": "^5.68.0", "@tanstack/router-plugin": "^1.114.27", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.4"}}