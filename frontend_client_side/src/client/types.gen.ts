// This file is auto-generated by @hey-api/openapi-ts

export type AboutUs = {
    title: string;
    description?: (string | null);
    index: number;
    image?: (string | null);
    status?: boolean;
    id?: (number | null);
};

export type AboutUsPublic = {
    data: Array<Introduction>;
    count: number;
};

export type Blog = {
    title: string;
    description?: (string | null);
    image1?: (string | null);
    image2?: (string | null);
    image3?: (string | null);
    status?: boolean;
    id?: (number | null);
    created_at?: string;
};

export type BlogsPublic = {
    data: Array<Blog>;
    count: number;
};

export type Body_aboutus_create_about_us = {
    description: string;
    title: string;
    image: (Blob | File);
    index: number;
};

export type Body_aboutus_edit_about_us = {
    description: string;
    image?: (Blob | File);
    title: string;
    index: number;
};

export type Body_blogs_create_blog = {
    description: string;
    title: string;
    image1?: ((Blob | File) | null);
    image2?: ((Blob | File) | null);
    image3?: ((Blob | File) | null);
};

export type Body_blogs_delete_blog_images = {
    image: string;
};

export type Body_blogs_edit_blog = {
    description: string;
    image1?: ((Blob | File) | null);
    image2?: ((Blob | File) | null);
    image3?: ((Blob | File) | null);
    title: string;
};

export type Body_clients_client_login_access_token = {
    grant_type?: (string | null);
    username: string;
    password: string;
    scope?: string;
    client_id?: (string | null);
    client_secret?: (string | null);
};

export type Body_introduction_create_introduction = {
    description: string;
    title: string;
    image: (Blob | File);
    index: number;
};

export type Body_introduction_edit_introduction = {
    description: string;
    image?: (Blob | File);
    title: string;
    index: number;
};

export type Body_login_login_access_token = {
    grant_type?: (string | null);
    username: string;
    password: string;
    scope?: string;
    client_id?: (string | null);
    client_secret?: (string | null);
};

export type Body_ma_course_create_macourse = {
    image: (Blob | File);
    index: number;
};

export type Body_ma_course_edit_macourse = {
    index: number;
};

export type Body_questionnaires_import_survey_questions = {
    file: (Blob | File);
};

export type ClientCreate = {
    email: string;
    is_active?: boolean;
    full_name?: (string | null);
    phone_number?: (string | null);
    password: string;
};

export type ClientPublic = {
    email: string;
    is_active?: boolean;
    full_name?: (string | null);
    phone_number?: (string | null);
    created_at?: string;
    id: string;
};

export type ClientRegister = {
    email: string;
    password: string;
    full_name?: (string | null);
    phone_number?: (string | null);
};

export type ClientsPublic = {
    data: Array<ClientPublic>;
    count: number;
};

export type ClientUpdate = {
    email?: (string | null);
    is_active?: (boolean | null);
    password?: (string | null);
    full_name?: (string | null);
    phone_number?: (string | null);
};

export type ClientUpdateMe = {
    full_name?: (string | null);
    email?: (string | null);
    phone_number?: (string | null);
};

export type HTTPValidationError = {
    detail?: Array<ValidationError>;
};

export type Introduction = {
    title: string;
    description?: (string | null);
    index: number;
    image?: (string | null);
    status?: boolean;
    id?: (number | null);
};

export type IntroductionCreate = {
    title: string;
    description?: (string | null);
    index: number;
    image?: (string | null);
    status?: boolean;
};

export type IntroductionsPublic = {
    data: Array<Introduction>;
    count: number;
};

export type ItemCreate = {
    title: string;
    description?: (string | null);
};

export type ItemPublic = {
    title: string;
    description?: (string | null);
    id: string;
    owner_id: string;
};

export type ItemsPublic = {
    data: Array<ItemPublic>;
    count: number;
};

export type ItemUpdate = {
    title?: (string | null);
    description?: (string | null);
};

export type MaCourseImage = {
    image: string;
    index: number;
    status?: boolean;
    id?: string;
};

export type MaCourseImagesPublic = {
    data: Array<MaCourseImage>;
    count: number;
};

export type Message = {
    message: string;
};

export type NewPassword = {
    token: string;
    new_password: string;
};

export type PrivateUserCreate = {
    email: string;
    password: string;
    full_name: string;
    is_verified?: boolean;
};

export type QuestionImportRow = {
    intelligence_category: string;
    core_competency: string;
    question: string;
};

export type QuestionnaireAnswerRequest = {
    question_id: number;
    selected_answer: string;
};

export type QuestionnaireCreate = {
    client_id: string;
};

export type QuestionnairePublic = {
    name: string;
    birth_info?: (string | null);
    caregiver?: (string | null);
    feeding?: (string | null);
    native_language?: (string | null);
    status?: QuestionnaireStatusEnum;
    id: string;
    client_id: string;
    survey_type_id: number;
    created_at: string;
    finished_at: (string | null);
    questions_and_ans: {
        [key: string]: unknown;
    };
};

export type QuestionnairesPublic = {
    data: Array<QuestionnaireWithDetails>;
    count: number;
};

export type QuestionnaireStartRequest = {
    name: string;
    birth_info?: (string | null);
    caregiver?: (string | null);
    feeding?: (string | null);
    native_language?: (string | null);
    survey_type_id: number;
};

export type QuestionnaireStatusEnum = 'available' | 'finished';

export type QuestionnaireSubmitRequest = {
    answers: Array<QuestionnaireAnswerRequest>;
};

export type QuestionnaireWithDetails = {
    id: string;
    client_id: string;
    survey_type_id: (number | null);
    survey_type_name: (string | null);
    name: string;
    birth_info: (string | null);
    caregiver: (string | null);
    feeding: (string | null);
    native_language: (string | null);
    status: QuestionnaireStatusEnum;
    created_at: string;
    finished_at: (string | null);
    questions_and_ans: {
        [key: string]: unknown;
    };
};

export type QuestionPublic = {
    content: string;
    index: number;
    competency_id: number;
    survey_type_id: number;
    question_id: number;
};

export type QuestionsImport = {
    questions: Array<QuestionImportRow>;
};

export type QuestionUpdate = {
    content?: (string | null);
    index?: (number | null);
};

export type QuestionWithDetails = {
    question_id: number;
    content: string;
    index: number;
    intelligence_category: string;
    core_competency: string;
};

export type Setting = {
    address: string;
    latitude: number;
    longitude: number;
    phone: string;
    email: string;
    facebook: string;
    whatsapp: string;
    id?: string;
};

export type SettingBase = {
    address: string;
    latitude: number;
    longitude: number;
    phone: string;
    email: string;
    facebook: string;
    whatsapp: string;
};

export type SurveyQuestionsPublic = {
    data: Array<QuestionWithDetails>;
    count: number;
};

export type SurveyTypePublic = {
    name: string;
    survey_type_id: number;
};

export type Token = {
    access_token: string;
    token_type?: string;
};

export type UpdatePassword = {
    current_password: string;
    new_password: string;
};

export type UserCreate = {
    email: string;
    is_active?: boolean;
    is_superuser?: boolean;
    full_name?: (string | null);
    password: string;
};

export type UserPublic = {
    email: string;
    is_active?: boolean;
    is_superuser?: boolean;
    full_name?: (string | null);
    id: string;
};

export type UserRegister = {
    email: string;
    password: string;
    full_name?: (string | null);
};

export type UsersPublic = {
    data: Array<UserPublic>;
    count: number;
};

export type UserUpdate = {
    email?: (string | null);
    is_active?: boolean;
    is_superuser?: boolean;
    full_name?: (string | null);
    password?: (string | null);
};

export type UserUpdateMe = {
    full_name?: (string | null);
    email?: (string | null);
};

export type ValidationError = {
    loc: Array<(string | number)>;
    msg: string;
    type: string;
};

export type AboutusCreateAboutUsData = {
    formData: Body_aboutus_create_about_us;
};

export type AboutusCreateAboutUsResponse = (AboutUs);

export type AboutusReadAboutUsListData = {
    limit?: number;
    skip?: number;
};

export type AboutusReadAboutUsListResponse = (AboutUsPublic);

export type AboutusEditAboutUsData = {
    formData: Body_aboutus_edit_about_us;
    id: number;
};

export type AboutusEditAboutUsResponse = (AboutUs);

export type AboutusDeleteAboutUsData = {
    id: number;
};

export type AboutusDeleteAboutUsResponse = (Message);

export type AdminReadClientsData = {
    limit?: number;
    skip?: number;
    /**
     * Field to sort by
     */
    sortBy?: string;
    /**
     * Sort order: asc or desc
     */
    sortOrder?: string;
    /**
     * Filter by status: active, inactive, or all
     */
    statusFilter?: (string | null);
};

export type AdminReadClientsResponse = (ClientsPublic);

export type AdminCreateClientAdminData = {
    requestBody: ClientCreate;
};

export type AdminCreateClientAdminResponse = (ClientPublic);

export type AdminReadClientAdminData = {
    clientId: string;
};

export type AdminReadClientAdminResponse = (ClientPublic);

export type AdminUpdateClientAdminData = {
    clientId: string;
    requestBody: ClientUpdate;
};

export type AdminUpdateClientAdminResponse = (ClientPublic);

export type AdminDeleteClientAdminData = {
    clientId: string;
};

export type AdminDeleteClientAdminResponse = (Message);

export type BlogsCreateBlogData = {
    formData: Body_blogs_create_blog;
};

export type BlogsCreateBlogResponse = (Blog);

export type BlogsGetBlogsData = {
    limit?: number;
    skip?: number;
};

export type BlogsGetBlogsResponse = (BlogsPublic);

export type BlogsEditBlogData = {
    formData: Body_blogs_edit_blog;
    id: number;
};

export type BlogsEditBlogResponse = (Blog);

export type BlogsGetBlogData = {
    id: number;
};

export type BlogsGetBlogResponse = (Blog);

export type BlogsDeleteBlogData = {
    id: number;
};

export type BlogsDeleteBlogResponse = (Message);

export type BlogsDeleteBlogImagesData = {
    formData: Body_blogs_delete_blog_images;
    id: number;
};

export type BlogsDeleteBlogImagesResponse = (Blog);

export type ClientsClientLoginAccessTokenData = {
    formData: Body_clients_client_login_access_token;
};

export type ClientsClientLoginAccessTokenResponse = (Token);

export type ClientsRegisterClientData = {
    requestBody: ClientRegister;
};

export type ClientsRegisterClientResponse = (ClientPublic);

export type ClientsVerifyEmailData = {
    token: string;
};

export type ClientsVerifyEmailResponse = (Message);

export type ClientsReadClientMeResponse = (ClientPublic);

export type ClientsUpdateClientMeData = {
    requestBody: ClientUpdateMe;
};

export type ClientsUpdateClientMeResponse = (ClientPublic);

export type IntroductionCreateIntroductionDocsData = {
    requestBody: IntroductionCreate;
};

export type IntroductionCreateIntroductionDocsResponse = (Introduction);

export type IntroductionCreateIntroductionData = {
    formData: Body_introduction_create_introduction;
};

export type IntroductionCreateIntroductionResponse = (Introduction);

export type IntroductionReadIntroductionListData = {
    limit?: number;
    skip?: number;
};

export type IntroductionReadIntroductionListResponse = (IntroductionsPublic);

export type IntroductionEditIntroductionData = {
    formData: Body_introduction_edit_introduction;
    id: number;
};

export type IntroductionEditIntroductionResponse = (Introduction);

export type IntroductionDeleteIntroductionData = {
    id: number;
};

export type IntroductionDeleteIntroductionResponse = (Message);

export type ItemsReadItemsData = {
    limit?: number;
    skip?: number;
};

export type ItemsReadItemsResponse = (ItemsPublic);

export type ItemsCreateItemData = {
    requestBody: ItemCreate;
};

export type ItemsCreateItemResponse = (ItemPublic);

export type ItemsReadItemData = {
    id: string;
};

export type ItemsReadItemResponse = (ItemPublic);

export type ItemsUpdateItemData = {
    id: string;
    requestBody: ItemUpdate;
};

export type ItemsUpdateItemResponse = (ItemPublic);

export type ItemsDeleteItemData = {
    id: string;
};

export type ItemsDeleteItemResponse = (Message);

export type LoginLoginAccessTokenData = {
    formData: Body_login_login_access_token;
};

export type LoginLoginAccessTokenResponse = (Token);

export type LoginTestTokenResponse = (UserPublic);

export type LoginRecoverPasswordData = {
    email: string;
};

export type LoginRecoverPasswordResponse = (Message);

export type LoginResetPasswordData = {
    requestBody: NewPassword;
};

export type LoginResetPasswordResponse = (Message);

export type LoginRecoverPasswordHtmlContentData = {
    email: string;
};

export type LoginRecoverPasswordHtmlContentResponse = (string);

export type MaCourseCreateMacourseData = {
    formData: Body_ma_course_create_macourse;
};

export type MaCourseCreateMacourseResponse = (MaCourseImage);

export type MaCourseGetMacoursesData = {
    limit?: number;
    skip?: number;
};

export type MaCourseGetMacoursesResponse = (MaCourseImagesPublic);

export type MaCourseEditMacourseData = {
    formData: Body_ma_course_edit_macourse;
    id: string;
};

export type MaCourseEditMacourseResponse = (MaCourseImage);

export type MaCourseDeleteMacourseData = {
    id: string;
};

export type MaCourseDeleteMacourseResponse = (unknown);

export type PrivateCreateUserData = {
    requestBody: PrivateUserCreate;
};

export type PrivateCreateUserResponse = (UserPublic);

export type QuestionnairesGetSurveyTypesResponse = (Array<SurveyTypePublic>);

export type QuestionnairesGetSurveyQuestionsData = {
    surveyTypeId: number;
};

export type QuestionnairesGetSurveyQuestionsResponse = (SurveyQuestionsPublic);

export type QuestionnairesImportSurveyQuestionsJsonData = {
    requestBody: QuestionsImport;
    surveyTypeId: number;
};

export type QuestionnairesImportSurveyQuestionsJsonResponse = (Message);

export type QuestionnairesImportSurveyQuestionsData = {
    formData: Body_questionnaires_import_survey_questions;
    surveyTypeId: number;
};

export type QuestionnairesImportSurveyQuestionsResponse = (Message);

export type QuestionnairesUpdateQuestionData = {
    questionId: number;
    requestBody: QuestionUpdate;
};

export type QuestionnairesUpdateQuestionResponse = (QuestionPublic);

export type QuestionnairesGetClientQuestionnairesData = {
    clientId: string;
};

export type QuestionnairesGetClientQuestionnairesResponse = (QuestionnairesPublic);

export type QuestionnairesCreateClientQuestionnaireData = {
    clientId: string;
    requestBody: QuestionnaireCreate;
};

export type QuestionnairesCreateClientQuestionnaireResponse = (QuestionnairePublic);

export type QuestionnairesUpdateQuestionnaireStatusData = {
    questionnaireId: string;
    status: QuestionnaireStatusEnum;
};

export type QuestionnairesUpdateQuestionnaireStatusResponse = (QuestionnairePublic);

export type QuestionnairesDeleteQuestionnaireData = {
    questionnaireId: string;
};

export type QuestionnairesDeleteQuestionnaireResponse = (Message);

export type QuestionnairesGetMyAvailableQuestionnaireResponse = ((QuestionnairePublic | null));

export type QuestionnairesStartMyQuestionnaireData = {
    requestBody: QuestionnaireStartRequest;
};

export type QuestionnairesStartMyQuestionnaireResponse = (QuestionnairePublic);

export type QuestionnairesSubmitQuestionnaireAnswersData = {
    requestBody: QuestionnaireSubmitRequest;
};

export type QuestionnairesSubmitQuestionnaireAnswersResponse = (QuestionnairePublic);

export type QuestionnairesFinishMyQuestionnaireResponse = (QuestionnairePublic);

export type QuestionnairesGetPublicSurveyTypesResponse = (Array<SurveyTypePublic>);

export type QuestionnairesGetPublicSurveyQuestionsData = {
    surveyTypeId: number;
};

export type QuestionnairesGetPublicSurveyQuestionsResponse = (SurveyQuestionsPublic);

export type SettingReadSettingResponse = (Setting);

export type SettingUpdateSettingData = {
    requestBody: SettingBase;
};

export type SettingUpdateSettingResponse = (SettingBase);

export type UsersReadUsersData = {
    limit?: number;
    skip?: number;
};

export type UsersReadUsersResponse = (UsersPublic);

export type UsersCreateUserData = {
    requestBody: UserCreate;
};

export type UsersCreateUserResponse = (UserPublic);

export type UsersReadUserMeResponse = (UserPublic);

export type UsersDeleteUserMeResponse = (Message);

export type UsersUpdateUserMeData = {
    requestBody: UserUpdateMe;
};

export type UsersUpdateUserMeResponse = (UserPublic);

export type UsersUpdatePasswordMeData = {
    requestBody: UpdatePassword;
};

export type UsersUpdatePasswordMeResponse = (Message);

export type UsersRegisterUserData = {
    requestBody: UserRegister;
};

export type UsersRegisterUserResponse = (UserPublic);

export type UsersReadUserByIdData = {
    userId: string;
};

export type UsersReadUserByIdResponse = (UserPublic);

export type UsersUpdateUserData = {
    requestBody: UserUpdate;
    userId: string;
};

export type UsersUpdateUserResponse = (UserPublic);

export type UsersDeleteUserData = {
    userId: string;
};

export type UsersDeleteUserResponse = (Message);

export type UtilsTestEmailData = {
    emailTo: string;
};

export type UtilsTestEmailResponse = (Message);

export type UtilsHealthCheckResponse = (boolean);