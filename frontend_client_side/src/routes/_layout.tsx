import { Flex } from "@chakra-ui/react"
import { Outlet, createFileRoute } from "@tanstack/react-router"
import MenuBar from "../components/menubar"
import Footer from '../components/footer'


export const Route = createFileRoute("/_layout")({
  component: Layout,
  // beforeLoad: async () => {
  //   if (!isLoggedIn()) {
  //     throw redirect({
  //       to: "/login",
  //     })
  //   }
  // },
})

function Layout() {
  return (
    <Flex direction="column" h = 'auto' >
      <MenuBar />
      <Flex  direction="column" overflowY="auto">
        <Outlet />
      </Flex>
      <Footer />
    </Flex>
  )
}

export default Layout
