import { createFile<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { Stack, Flex, Image, Button, Box } from "@chakra-ui/react"
import { motion } from "framer-motion"
import { ChineseText } from "../../components/ui/fonts";
import useAuth from "../../hooks/useAuth";
import { type ClientRegister } from '../../client'
import { type SubmitHandler, useForm } from "react-hook-form"

type SignUpForm = ClientRegister & {
    confirmPassword: string
}
import { colors } from "@/colors"
import { Field } from "../../components/ui/field"
import { FormInput } from "../../components/ui/formInput"
import { FormButton } from "../../components/ui/formButton"
import { MotionStack } from "../../components/ui/motionStack";

export const Route = createFileRoute('/_layout/signup')({
    component: RouteComponent,
})

function RouteComponent() {
    const { signUpMutation, error, resetError } = useAuth()
    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
    } = useForm<SignUpForm>({
        mode: "onBlur",
        criteriaMode: "all",
        defaultValues: {
            email: "",
            password: "",
            confirmPassword: "",
            full_name: "",
            phone_number: ""
        },
    })

    const onSubmit: SubmitHandler<ClientRegister> = async (data) => {
        if (isSubmitting) return

        resetError()

        try {
            await signUpMutation.mutateAsync(data)
        } catch {
            // error is handled by useAuth hook
        }
    }

    // Animation variants (same as signin.tsx)
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2,
            }
        }
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                type: "spring",
                damping: 12,
                stiffness: 100
            }
        }
    };

    return (
        <Stack
            w={'full'}
            justify={'center'}
            align={'center'}
        >
            <Flex
                w={{
                    base: 'full',
                    md: '90%',
                }}
                mt={{
                    base: '0',
                    md: '200px',
                }}
                direction={{
                    base: 'column',
                    md: 'row',
                }}
            >
                <Stack
                    w={{ base: 'full', md: '50%' }}
                    justify={'center'}
                    align={'center'}
                    order={{ base: 2, md: 1 }}
                >
                    <MotionStack
                        w={{ base: '90%', md: '60%' }}
                        mt={{ base: '40px', md: '0px' }}
                        gap={3}
                        as="form"
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                        onSubmit={handleSubmit(onSubmit)}
                    >
                        <motion.div variants={itemVariants}>
                            <ChineseText
                                fontSize={'3xl'}
                                color={'black'}
                            >
                                {"註冊帳號"}
                            </ChineseText>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Flex
                                gap={2}
                                mt={1}
                            >
                                <ChineseText
                                    color='black'>
                                    {"已有帳號嗎？"}
                                </ChineseText>
                                <Link to="/signin">
                                    <ChineseText
                                        color={colors.mainColor}
                                        cursor="pointer"
                                        _hover={{ textDecoration: 'underline' }}
                                    >
                                        {"登入"}
                                    </ChineseText>
                                </Link>
                            </Flex>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Field
                                mt={5}
                                hideRequiredIndicator={true}
                                color={'black'}
                                invalid={!!errors.full_name}
                                errorText={errors.full_name?.message}
                                label="家長姓名"
                            >
                                <FormInput
                                    id="full_name"
                                    {...register("full_name")}
                                    placeholder="請輸入全名"
                                    type="text"
                                />
                            </Field>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Field
                                hideRequiredIndicator={true}
                                color={'black'}
                                invalid={!!errors.phone_number}
                                errorText={errors.phone_number?.message}
                                label="電話號碼"
                            >
                                <FormInput
                                    id="phone_number"
                                    {...register("phone_number", {
                                        pattern: {
                                            value: /^[0-9]{8,15}$/,
                                            message: "無效的電話號碼"
                                        }
                                    })}
                                    placeholder="請輸入電話號碼"
                                    type="tel"
                                />
                            </Field>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Field
                                hideRequiredIndicator={true}
                                color={'black'}
                                required
                                invalid={!!errors.email}
                                errorText={errors.email?.message}
                                label="電郵"
                            >
                                <FormInput
                                    id="email"
                                    {...register("email", {
                                        required: "必須要填寫電郵",
                                        pattern: {
                                            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                            message: "無效的電郵地址"
                                        }
                                    })}
                                    placeholder="<EMAIL>"
                                    type="email"
                                />
                            </Field>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Field
                                hideRequiredIndicator={true}
                                color={'black'}
                                required
                                invalid={!!errors.password}
                                errorText={errors.password?.message}
                                label="密碼"
                            >
                                <FormInput
                                    id="password"
                                    {...register("password", {
                                        required: "必須要填寫密碼",
                                        minLength: {
                                            value: 8,
                                            message: "密碼最少8位數字"
                                        }
                                    })}
                                    placeholder="最少8位數字"
                                    type="password"
                                />
                            </Field>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Field
                                hideRequiredIndicator={true}
                                color={'black'}
                                required
                                invalid={!!errors.confirmPassword}
                                errorText={errors.confirmPassword?.message}
                                label="確認密碼"
                            >
                                <FormInput
                                    id="confirmPassword"
                                    {...register("confirmPassword", {
                                        required: "請再次輸入密碼",
                                        validate: (value, formValues) =>
                                            value === formValues.password || "密碼不一致"
                                    })}
                                    placeholder="請再次輸入密碼"
                                    type="password"
                                />
                            </Field>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <FormButton
                                type="submit"
                                loading={isSubmitting}
                            >
                                {"註冊"}
                            </FormButton>
                        </motion.div>
                    </MotionStack>
                </Stack>

                <Stack
                    w={{ base: 'full', md: '50%' }}
                    order={{ base: 1, md: 2 }}
                >
                    <Box position="relative" w="full" h={{ md: '700px', lg: '650px', xl: '650px' }}>
                        <motion.img
                            src='/images/signin.jpeg'
                            style={{
                                borderRadius: '0.75rem',
                                objectFit: 'cover',
                                width: '100%',
                                height: '100%'
                            }}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 0.7 }}
                            transition={{
                                duration: 0.8,
                                delay: 0.2
                            }}
                        />
                    </Box>
                </Stack>
            </Flex>
        </Stack>
    )
}
