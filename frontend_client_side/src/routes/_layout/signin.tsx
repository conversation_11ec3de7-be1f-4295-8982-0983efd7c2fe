import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { <PERSON>ack, Flex, Image, Button } from "@chakra-ui/react"
import { motion } from "framer-motion"
import { ChineseText } from "../../components/ui/fonts";
import useAuth from "../../hooks/useAuth";
import { type Body_clients_client_login_access_token as AccessToken } from '../../client'
import { type SubmitHandler, useForm } from "react-hook-form"
import { colors } from "@/colors"
import { Field } from "../../components/ui/field"
import { FormInput } from "../../components/ui/formInput"
import { FormButton } from "../../components/ui/formButton"
import { MotionStack } from "../../components/ui/motionStack";
export const Route = createFileRoute('/_layout/signin')({
    component: RouteComponent,
})

function RouteComponent() {

    const { loginMutation, error, resetError } = useAuth()
    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
    } = useForm<AccessToken>({
        mode: "onBlur",
        criteriaMode: "all",
        defaultValues: {
            username: "",
            password: "",
        },
    })

    const onSubmit: SubmitHandler<AccessToken> = async (data) => {
        if (isSubmitting) return

        resetError()

        try {
            await loginMutation.mutateAsync(data)
        } catch {
            // error is handled by useAuth hook
        }
    }


    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2,
            }
        }
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                type: "spring",
                damping: 12,
                stiffness: 100
            }
        }
    };


    return (
        <Stack
            w={'full'}
            justify={'center'}
            align={'center'}
        >



            <Flex
                w={{
                    base: 'full',
                    md: '90%',
                }}
                mt={{
                    base: '0',
                    md: '200px',
                }}
                direction={{
                    base: 'column',
                    md: 'row',
                }}
            >

                <Stack
                    w={{ base: 'full', md: '50%' }}
                    justify={'center'}
                    align={'center'}
                    order={{ base: 2, md: 1 }}
                >

                    <MotionStack
                        w={{ base: '90%', md: '60%' }}
                        mt={{ base: '40px', md: '0px' }}
                        gap={3}
                        as="form"
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                    >
                        <motion.div variants={itemVariants}>
                            <ChineseText
                                fontSize={'3xl'}
                                color={'black'}
                            >
                                {"歡迎回來"}
                            </ChineseText>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <ChineseText
                                fontSize={'lg'}
                                color={'black'}
                                mb={5}
                            >
                                {"解鎖成長基因，科學測評孩子的未來潛能！"}
                            </ChineseText>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Field
                                hideRequiredIndicator={true}
                                color={'black'}
                                required
                                invalid={!!errors.username}
                                errorText={errors.username?.message}
                                label="電郵"
                            >
                                <FormInput
                                    id="username"
                                    {...register("username", {
                                        required: "必須要填寫電郵",
                                    })}
                                    placeholder="<EMAIL>"
                                    type="email"
                                />
                            </Field>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Field
                                hideRequiredIndicator={true}
                                color={'black'}
                                required
                                invalid={!!errors.password}
                                errorText={errors.password?.message}
                                label="密碼"
                            >
                                <FormInput
                                    id="password"
                                    {...register("password", {
                                        required: "必須要填寫密碼",
                                    })}
                                    placeholder="最少8位數字"
                                    type="password"
                                />
                            </Field>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Flex
                                w='full'
                                justify={'flex-end'}
                            >
                                <ChineseText
                                    color={colors.mainColor}
                                >
                                    {"忘記密碼?"}
                                </ChineseText>
                            </Flex>
                        </motion.div>

                        <motion.div variants={itemVariants}
                        >
                            <FormButton
                                onClick={handleSubmit(onSubmit)}
                                loading={isSubmitting}
                            >
                                {"登入"}
                            </FormButton>
                        </motion.div>

                        <motion.div variants={itemVariants}>
                            <Flex
                                w='full'
                                justify={'center'}
                                gap={2}
                                mt={5}
                            >
                                <ChineseText
                                    color='black'>
                                    {"沒有帳號嗎？"}
                                </ChineseText>
                                <Link to="/signup">
                                    <ChineseText
                                        color={colors.mainColor}
                                        cursor="pointer"
                                        _hover={{ textDecoration: 'underline' }}
                                    >
                                        {"註冊"}
                                    </ChineseText>
                                </Link>
                            </Flex>
                        </motion.div>
                    </MotionStack>


                </Stack>

                <Stack
                    w={{ base: 'full', md: '50%' }}
                    order={{ base: 1, md: 2 }}
                >
                    <motion.img 
                        src='/images/signin.jpeg'
                        style={{
                            borderRadius: '0.75rem',
                            objectFit: 'cover',
                            width: '100%',
                            height: '100%'
                        }}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 0.7 }}
                        transition={{ 
                            duration: 0.8,
                            delay: 0.2
                        }}
                    />

                </Stack>



            </Flex>

        </Stack>

    )

}
