import { createFileRoute } from "@tanstack/react-router"
import { Image, Box, Stack, Flex,useBreakpointValue } from "@chakra-ui/react"
import Header from '@/components/ui/header'

import { z } from "zod"
import { AboutusService } from '@/client/'

import { useQuery } from "@tanstack/react-query"
import { AboutUs } from '@/client/types.gen'
import { ChineseText } from "../../components/ui/fonts";
import { motion } from "framer-motion"

const imageBaseUrl = import.meta.env.VITE_IMAGE_URL;
export const Route = createFileRoute('/_layout/aboutus')({
    component: RouteComponent,
    validateSearch: (search) => activitysSearchSchema.parse(search),
})

const topText =
    `我們的使命
我們相信每個孩子都是獨特的智慧載體。
透過前沿的認知科學與教育心理學研究，我們致力於打造精準、全面的兒童智慧評估體系，幫助家長與教育者透視孩子的核心潛能，為個人化成長提供科學導航。`

const activitysSearchSchema = z.object({
    page: z.number().catch(1),
})

const PER_PAGE = 20
function getAboutUsQueryOptions({ page }: { page: number }) {
    return {
        queryFn: () =>
            AboutusService.readAboutUsList({ skip: (page - 1) * PER_PAGE, limit: PER_PAGE, }),
        queryKey: ["aboutUs", { page }],
    }
}



const AboutUsItem = ({ item, index }: { item: AboutUs; index: number }) => {
    const isSmallScreen = useBreakpointValue({ base: true, sm: true, md: false })
    const isOddIndex = index % 2 !== 0


    // For small screens, always stack vertically with image on top
    if (isSmallScreen) {
        return (
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                    type: "spring",
                    stiffness: 100,
                    damping: 10,
                    delay: index * 0.1
                }}
            >
                <Stack
                    w="full"
                    gap={4}
                    my={8}
                >
                    <Box
                        overflow="hidden"
                        borderRadius="md"
                    >
                        <Image
                            src={`${imageBaseUrl}${item.image}`}
                            alt={item.title}
                            w="full"
                            opacity={0.7}
                            objectFit="cover"
                        />
                    </Box>
                    <Stack
                        ml='5%'
                    >
                        <ChineseText fontSize="xl" fontWeight="bold" color='black'>{item.title}</ChineseText>
                        <ChineseText whiteSpace="pre-line" color='black'>{item.description}</ChineseText>
                    </Stack>
                </Stack>
            </motion.div>
        )
    }

    // For larger screens, alternate layout based on index
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
                type: "spring",
                stiffness: 100,
                damping: 10,
                delay: index * 0.1
            }}
        >
            <Flex
                w="full"
                direction={isOddIndex ? "row-reverse" : "row"}
                justify="space-between"

                my={12}
                gap={8}
            >
                <Stack
                    w="60%"
                    overflow="hidden"
                    h={{ md: '400px', lg: '500px', xl: '600px' }}


                >
                    <Image
                        src={`${imageBaseUrl}${item.image}`}
                        alt={item.title}
                        w="100%"
                        minH={{ md: '400px', lg: '500px', xl: '600px' }}

                        fit='cover'
                        objectPosition='left'
                        opacity={0.7}
                    />
                </Stack>
                <Stack
                    w="40%"
                    h={{ md: '400px', lg: '500px', xl: '600px' }}

                    ml={isOddIndex ? '5%' : 0}
                    justify={'flex-end'}
                >
                    <ChineseText fontSize="2xl" fontWeight="bold" color='black'>{item.title}</ChineseText>
                    <ChineseText whiteSpace="pre-line" color='black'>{item.description}</ChineseText>
                </Stack>
            </Flex>
        </motion.div>
    )
}

function RouteComponent() {

    const { data } = useQuery({
        ...getAboutUsQueryOptions({ page: 1 }),
        placeholderData: (prevData) => prevData,
    })

    const items = (data?.data.slice(0, PER_PAGE) ?? [])
        .sort((a, b) => a.index - b.index);

    return <Stack
        w='full'
        justify={'center'}
        align={'center'}
    >
        <Header title={'關於我們'} />

        <Stack
            w='100%'
            align={'flex-end'}
            mt={'100px'}
        >
            <Stack
                w="70%"
            >
                <ChineseText
                    w='80%'

                    color='black'
                    fontSize={'xl'}
                    fontWeight={'bold'}
                    whiteSpace="pre-line"
                >
                    {topText}

                </ChineseText>
            </Stack>

            {items.map((item, index) => (
                <AboutUsItem key={item.id} item={item} index={index} />
            ))}


        </Stack>
        <Stack
            w='full'
            align={'center'}
            justify={'center'}
            mt={'200px'}
        >
            <ChineseText


                color='black'
                fontSize={'xl'}
                fontWeight={'bold'}
                whiteSpace="pre-line"
            >
                {'寫給家長的一封信'}

            </ChineseText>

            <ChineseText
                color='black'
                fontSize={'lg'}
                textAlign={'center'}
                mt={5}
                whiteSpace="pre-line"
            >
                {`「當我們談論『智能』時，本質是在探索孩子與世界的對話方式。
我們的工作，是讓您聽見那些尚未被語言表達的潛能之聲。
從評估到賦能，我們與您共同見證成長的奇蹟。 」`}

            </ChineseText>

        </Stack>

        <Image
            w='full'
            my={'100px'}
            h='500px'
            opacity={0.7}
            fit={'cover'}
            src='/images/background.jpeg'

        />

    </Stack>
}
