import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { Image, Box, Stack, Flex, Button, SimpleGrid, Text } from "@chakra-ui/react"
import Header from '@/components/ui/header'
import moment from 'moment'
import { z } from "zod"
import { BlogsService } from '@/client/'
import { colors } from '../../colors'
import { useQuery } from "@tanstack/react-query"
import { Blog } from '@/client/types.gen'
import { ChineseText } from "../../components/ui/fonts";
import { motion } from "motion/react"
import { useState, useEffect } from "react"
const imageBaseUrl = import.meta.env.VITE_IMAGE_URL;
export const Route = createFileRoute('/_layout/activitys')({
    component: RouteComponent,
    validateSearch: (search) => activitysSearchSchema.parse(search),
})

const activitysSearchSchema = z.object({
    page: z.number().catch(1),
})


const PER_PAGE = 10



function RouteComponent() {


    const { page } = Route.useSearch()

    const navigate = useNavigate({ from: Route.fullPath })
    const [allItems, setAllItems] = useState<Blog[]>([])

    function getActivitysQueryOptions({ page }: { page: number }) {
        return {
            queryFn: () =>
                BlogsService.getBlogs({ skip: (page - 1) * PER_PAGE, limit: PER_PAGE, }),
            queryKey: ["activitys", { page }],
        }
    }

    const { data, isLoading, isPlaceholderData } = useQuery({
        ...getActivitysQueryOptions({ page }),
        placeholderData: (prevData) => prevData,
    })

    // Update allItems when new data comes in
    useEffect(() => {
        if (data?.data && !isPlaceholderData) {
            // For page 1, replace the items
            if (page === 1) {
                setAllItems(data.data);
            } else {
                // For other pages, append new items that don't already exist
                const newItems = data.data.filter(
                    newItem => !allItems.some(item => item.id === newItem.id)
                );
                setAllItems(prev => [...prev, ...newItems]);
            }
        }
    }, [data, page, isPlaceholderData]);

    const setPage = (page: number) =>
        navigate({
            search: (prev: { page: number }) => ({ ...prev, page }),
        })




    // Add sorting by created_at date (newest first) using moment

    const count = data?.count ?? 0

    // Sort all accumulated items
    const sortedItems = [...allItems].sort((a, b) => {
        const dateA = a.created_at ? moment(a.created_at) : moment(0);
        const dateB = b.created_at ? moment(b.created_at) : moment(0);
        return dateB.valueOf() - dateA.valueOf();
    });

    const activityItem = (item: Blog, index: number, pageNumber: number) => {
        const imageSrc = item.image1
            ? `${imageBaseUrl}${item.image1}`
            : item.image2
                ? `${imageBaseUrl}${item.image2}`
                : item.image3
                    ? `${imageBaseUrl}${item.image3}`
                    : '/images/main1.webp';

        return (
            <motion.div
                key={`${item.id}-${pageNumber}`}
                initial={{ opacity: pageNumber === page ? 0 : 1, scale: pageNumber === page ? 0.95 : 1 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{
                    duration: 0.3,
                    delay: pageNumber === page ? (index % PER_PAGE) * 0.1 : 0,
                    ease: "easeOut"
                }}
                style={{ width: '100%' }}
                onClick={() => {
                    item.id && navigate({
                        to: "/activity/$id",
                        params: {
                            id: item.id.toString()
                        },
                    
                    })
                }}
            >
    <Stack w={'full'} bgColor='white'>
        <Image src={imageSrc} />
        <Stack h={"70px"}>
            <ChineseText
                mt={4}
                color='black'
                fontSize={"lg"}
                mx={2}
            >
                {item.title}
            </ChineseText>
        </Stack>
        <Stack h={"30px"}>
            <ChineseText
                color='#6C757D'
                fontSize={"md"}
                lineClamp={3}
                mx={2}
            >
                {moment(item.created_at).format('DD-MM-YYYY')}
            </ChineseText>
        </Stack>
        <Stack h={"110px"}>
            <ChineseText
                color='#6C757D'
                fontSize={"md"}
                lineClamp={3}
                mx={2}
            >
                {item.description}
            </ChineseText>
        </Stack>
    </Stack>
            </motion.div >
        );
    }

return <Stack
    w='full'
    justify={'center'}
    align={'center'}
>
    <Header title={'課程及活動'} />
    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }}
        gap={5}
        w={'90%'} >
        {
            sortedItems.map((item, index) => {
                const itemPage = Math.floor(index / PER_PAGE) + 1;
                return activityItem(item, index % PER_PAGE, itemPage);
            })
        }
    </SimpleGrid>
    <motion.div

        whileTap={{ scale: 0.95 }}
    >
        <Button
            mt={10}
            mb={5}
            w={'300px'}
            bgColor={colors.mainColor}
            color="white"
            _focus={{
                boxShadow: 'none',
                outline: 'none',
                border: 'none'
            }}
            onClick={() => {
                // Only go to next page if there are more items to load
                if (page * PER_PAGE < count) {
                    setPage(page + 1)
                }
            }}
        >
            {'更多'}
        </Button>
    </motion.div>
</Stack>
}
