import { createFileRoute, useParams } from '@tanstack/react-router'
import { useQuery } from '@tanstack/react-query'
import { BlogsService } from '@/client/'
import { Image, Box, Stack, Flex, Button, SimpleGrid, Text } from "@chakra-ui/react"
import { ChineseText } from "../../../components/ui/fonts";
import ImagesSilder from '@/components/ui/imagesSilder'

export const Route = createFileRoute('/_layout/activity/$id')({
  component: RouteComponent,
})

function getActivityQueryOptions(id: number) {
  return {
    queryKey: ['activity', id],
    queryFn: () => BlogsService.getBlog({ id }),
  }
}

function RouteComponent() {
  const { id: idStr } = useParams({ strict: false })
  const id = Number(idStr)
  const { data, isLoading, error } = useQuery(getActivityQueryOptions(id))

  if (!data) return <div>No data available</div>
  // Create images array filtering out both null and undefined values
  const images = [
    data.image1,
    data.image2,
    data.image3
  ].filter(image => image !== null && image !== undefined);




  return (
    <Stack
      w='full'
      h='auto'
      align={'center'}
    >
      <ImagesSilder images={images} />

      <Stack
        w='90%'
        mt={'50px'}
      >
        <ChineseText
          color='black'
          fontSize='4xl'
          fontWeight='bold'
        >
          {data.title}
        </ChineseText>

        <ChineseText
          color='black'
          fontSize='lg'
          mt={5}
          whiteSpace="pre-line"
        >
          {data.description}
        </ChineseText>

      </Stack>

    </Stack>
  )
}
