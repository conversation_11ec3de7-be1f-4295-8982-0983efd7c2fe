import {
  <PERSON>,
  But<PERSON>,
  Text,
  VStack,
  Flex,
  Badge,
  Input,
} from "@chakra-ui/react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { QuestionnairesService, type QuestionnaireStartRequest } from "@/client"
import Header from "@/components/header"
import { ChineseText } from "@/components/ui/fonts"
export const Route = createFileRoute("/_layout/questionnaire-start")({
  component: QuestionnaireStartPage,
})

const questionnaireStartSchema = z.object({
  name: z.string().min(1, "姓名為必填項目"),
  birth_info: z.string().optional(),
  caregiver: z.string().optional(),
  feeding: z.string().optional(),
  native_language: z.string().optional(),
  survey_type_id: z.number().min(1, "請選擇評估類型"),
})

type QuestionnaireStartData = z.infer<typeof questionnaireStartSchema>

function QuestionnaireStartPage() {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [selectedSurveyType, setSelectedSurveyType] = useState<number | null>(null)

  // Get available questionnaire for current client
  const { data: questionnaire, isLoading: questionnaireLoading } = useQuery({
    queryKey: ["my-questionnaire"],
    queryFn: () => QuestionnairesService.getMyAvailableQuestionnaire(),
    enabled: false, // Temporarily disable to test page rendering
  })

  // Get survey types
  const { data: surveyTypes, isLoading: surveyTypesLoading } = useQuery({
    queryKey: ["public-survey-types"],
    queryFn: () => QuestionnairesService.getPublicSurveyTypes(),
  })

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<QuestionnaireStartData>({
    resolver: zodResolver(questionnaireStartSchema),
  })

  const startQuestionnaireMutation = useMutation({
    mutationFn: (data: QuestionnaireStartRequest) =>
      QuestionnairesService.startMyQuestionnaire({ requestBody: data }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["my-questionnaire"] })
      navigate({ to: "/questionnaire-questions" })
    },
    onError: (error: any) => {
      console.error("Failed to start questionnaire:", error)
      alert("開始評估失敗: " + (error?.message || "未知錯誤"))
    },
  })

  const onSubmit = (data: QuestionnaireStartData) => {
    if (!selectedSurveyType) {
      alert("請選擇評估類型")
      return
    }

    const startData: QuestionnaireStartRequest = {
      ...data,
      survey_type_id: selectedSurveyType,
    }

    startQuestionnaireMutation.mutate(startData)
  }

  // Debug: Show loading and data states
  console.log("Questionnaire loading:", questionnaireLoading)
  console.log("Survey types loading:", surveyTypesLoading)
  console.log("Questionnaire data:", questionnaire)
  console.log("Survey types data:", surveyTypes)
  console.log("Survey types array?", Array.isArray(surveyTypes))
  console.log("Survey types length:", surveyTypes?.length)

  if (questionnaireLoading || surveyTypesLoading) {
    return (
      <Box maxW="4xl" py={8} mx="auto">
        <VStack gap={4}>
          <Text>載入中...</Text>
          <Text fontSize="sm" color="gray.500">
            Questionnaire loading: {questionnaireLoading ? "Yes" : "No"}
          </Text>
          <Text fontSize="sm" color="gray.500">
            Survey types loading: {surveyTypesLoading ? "Yes" : "No"}
          </Text>
        </VStack>
      </Box>
    )
  }

  // For now, let's show the page even without questionnaire data for testing
  // if (!questionnaire) {
  //   return (
  //     <Container maxW="4xl" py={8}>
  //       <VStack gap={6} textAlign="center">
  //         <Heading size="lg" color="red.500">
  //           <ChineseText>沒有可用的評估</ChineseText>
  //         </Heading>
  //         <ChineseText color="gray.600">
  //           您目前沒有可用的評估問卷，請聯繫客服人員。
  //         </ChineseText>
  //         <Button onClick={() => navigate({ to: "/" })}>
  //           <ChineseText>回到首頁</ChineseText>
  //         </Button>
  //       </VStack>
  //     </Container>
  //   )
  // }

  const termsSection = () => {
    return (
      <Flex
        width='full'
        justify={'center'}
        mt={'10'}
      >
        <Flex
          as="nav"
          bgColor={'white'}
          direction={'column'}
          w='90vw'
        >
          <Flex
            direction={'column'}
            m={'5'}
          >

            <ChineseText color={'black'} mb={2}>1. 評估目的與範圍</ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              1. 本評估旨在透過標準化工具與專業方法，了解兒童在認知、語言、邏輯推理等核心智能領域的發展狀況，為教育或介入方案提供參考。
            </ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              2. 評估內容及流程已通過倫理審查，不包含任何可能對兒童身心造成傷害的環節。
            </ChineseText>
            <ChineseText color={'black'} mb={4}>&nbsp;</ChineseText>

            <ChineseText color={'black'} mb={2}>2. 監護人知情同意</ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              1. 監護人有權知悉評估的具體內容、方法、預期時長及潛在風險，並可透過書面或口頭向評估機構提出詢問。
            </ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              2. 監護人需確保所提供兒童的健康狀況、教育背景等資訊真實有效，否則可能影響評估結果的準確性。
            </ChineseText>
            <ChineseText color={'black'} mb={4}>&nbsp;</ChineseText>

            <ChineseText color={'black'} mb={2}>3. 隱私與資料保護</ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              1. 評估過程中所取得的所有個人資訊（含測試結果、影像記錄等）將嚴格保密，未經監護人書面授權，不得向第三方揭露。
            </ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              2. 資料儲存符合《個人資訊保護法》要求，評估報告僅用於約定用途，銷毀週期為____年（具體時間需明確）。
            </ChineseText>
            <ChineseText color={'black'} mb={4}>&nbsp;</ChineseText>

            <ChineseText color={'black'} mb={2}>4. 責任限制</ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              1. 評估結果基於兒童當次表現及現有科學工具得出，受環境、情緒等因素影響，**不作為唯一診斷或法律依據**。
            </ChineseText>
            <ChineseText color={'black'} fontWeight="normal">
              2. 評估機構不承擔因監護人誤用、曲解報告內容所導致的直接或間接責任。
            </ChineseText>

          </Flex>

        </Flex>


      </Flex >
    )
  }

  const inputWidth = { base: 'full', md: '50%' }

  return (
    <Box bg="#f9f5e9" minH="100vh" position="relative">
      <Header title="智能核心評估" />

      {/* Terms and Conditions Section */}
      {termsSection()}

      {/* Survey Type Selection Cards */}
      <Box position="absolute" left="166px" top="900px" width="1588px">
        <VStack gap={6} align="stretch">
          {!surveyTypes || surveyTypes.length === 0 ? (
            <Box p={6} textAlign="center">
              <Text fontSize="18px" color="gray.600">
                {surveyTypesLoading ? "載入評估類型中..." : "暫無可用的評估類型"}
              </Text>
            </Box>
          ) : (
            surveyTypes.map((surveyType) => {
              const surveyId = surveyType.survey_type_id

              return (
                <Box
                  key={surveyId}
                  bg={selectedSurveyType === surveyId ? "#d3401f" : "#ffffff"}
                  color={selectedSurveyType === surveyId ? "#ffffff" : "#000000"}
                  border="1px solid #d4d7e3"
                  borderRadius="12px"
                  p={6}
                  cursor="pointer"
                  onClick={() => {
                    setSelectedSurveyType(surveyId)
                    setValue("survey_type_id", surveyId)
                  }}
                  _hover={{ borderColor: "#d3401f" }}
                >
                  <Flex justify="space-between" align="center">
                    <Box>
                      <Text fontFamily="Inter" fontWeight="bold" fontSize="24px" mb={2}>
                        {surveyType.name}
                      </Text>
                      <Text fontFamily="Inter" fontSize="16px" opacity={0.8}>
                        智能核心評估問卷
                      </Text>
                    </Box>
                    {selectedSurveyType === surveyId && (
                      <Badge bg="#ffffff" color="#d3401f" px={3} py={1} borderRadius="md">
                        已選擇
                      </Badge>
                    )}
                  </Flex>
                </Box>
              )
            })
          )}
        </VStack>
      </Box>

      {/* Personal Information Form */}
      {(
        <Box
          w='full'
          mt = '20'
          justifyItems={'center'}
        >
          <form onSubmit={handleSubmit(onSubmit)}>
            <VStack gap={6} align="stretch" w='90vw'>
              {/* Name Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  孩子姓名
                </Text>
                <Input
                  w={inputWidth}
                  {...register("name")}
                  placeholder="請輸入孩子姓名"
                  bg="#f7fbff"
                  border="1px solid #d4d7e3"
                  borderRadius="12px"
                  h="50px"
                  fontSize="16px"
                  color="#2d3748"
                  _placeholder={{ color: "#2d3748" }}
                />
                {errors.name && (
                  <Text color="red.500" fontSize="14px" mt={1}>
                    {errors.name.message}
                  </Text>
                )}
              </Box>

              {/* Birth Info Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  孩子出生方式
                </Text>
                <Box position="relative">
                  <select
                    {...register("birth_info")}
                    style={{
                      width: "100%",
                      height: "50px",
                      backgroundColor: "#f7fbff",
                      border: "1px solid #d4d7e3",
                      borderRadius: "12px",
                      fontSize: "16px",
                      color: "#2d3748",
                      paddingLeft: "16px",
                      paddingRight: "40px",
                      appearance: "none",
                      outline: "none"
                    }}
                  >
                    <option value="">順產</option>
                    <option value="順產">順產</option>
                    <option value="剖腹產">剖腹產</option>
                    <option value="其他">其他</option>
                  </select>
                  <Box
                    position="absolute"
                    right="12px"
                    top="50%"
                    transform="translateY(-50%)"
                    pointerEvents="none"
                    fontSize="12px"
                    color="#2d3748"
                  >
                    ▼
                  </Box>
                </Box>
              </Box>

              {/* Caregiver Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  孩子多數由誰照顧
                </Text>
                <Box position="relative">
                  <select
                    {...register("caregiver")}
                    style={{
                      width: "100%",
                      height: "50px",
                      backgroundColor: "#f7fbff",
                      border: "1px solid #d4d7e3",
                      borderRadius: "12px",
                      fontSize: "16px",
                      color: "#2d3748",
                      paddingLeft: "16px",
                      paddingRight: "40px",
                      appearance: "none",
                      outline: "none"
                    }}
                  >
                    <option value="">媽媽</option>
                    <option value="媽媽">媽媽</option>
                    <option value="爸爸">爸爸</option>
                    <option value="祖父母">祖父母</option>
                    <option value="外祖父母">外祖父母</option>
                    <option value="其他">其他</option>
                  </select>
                  <Box
                    position="absolute"
                    right="12px"
                    top="50%"
                    transform="translateY(-50%)"
                    pointerEvents="none"
                    fontSize="12px"
                    color="#2d3748"
                  >
                    ▼
                  </Box>
                </Box>
              </Box>

              {/* Feeding Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  孩子喂養
                </Text>
                <Box position="relative">
                  <select
                    {...register("feeding")}
                    style={{
                      width: "100%",
                      height: "50px",
                      backgroundColor: "#f7fbff",
                      border: "1px solid #d4d7e3",
                      borderRadius: "12px",
                      fontSize: "16px",
                      color: "#2d3748",
                      paddingLeft: "16px",
                      paddingRight: "40px",
                      appearance: "none",
                      outline: "none"
                    }}
                  >
                    <option value="">奶粉</option>
                    <option value="母乳">母乳</option>
                    <option value="奶粉">奶粉</option>
                    <option value="混合餵養">混合餵養</option>
                  </select>
                  <Box
                    position="absolute"
                    right="12px"
                    top="50%"
                    transform="translateY(-50%)"
                    pointerEvents="none"
                    fontSize="12px"
                    color="#2d3748"
                  >
                    ▼
                  </Box>
                </Box>
              </Box>

              {/* Native Language Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  母語
                </Text>
                <Box position="relative">
                  <select
                    {...register("native_language")}
                    style={{
                      width: "100%",
                      height: "50px",
                      backgroundColor: "#f7fbff",
                      border: "1px solid #d4d7e3",
                      borderRadius: "12px",
                      fontSize: "16px",
                      color: "#2d3748",
                      paddingLeft: "16px",
                      paddingRight: "40px",
                      appearance: "none",
                      outline: "none"
                    }}
                  >
                    <option value="">粵語</option>
                    <option value="粵語">粵語</option>
                    <option value="普通話">普通話</option>
                    <option value="英語">英語</option>
                    <option value="其他">其他</option>
                  </select>
                  <Box
                    position="absolute"
                    right="12px"
                    top="50%"
                    transform="translateY(-50%)"
                    pointerEvents="none"
                    fontSize="12px"
                    color="#2d3748"
                  >
                    ▼
                  </Box>
                </Box>
              </Box>

              {/* Submit Button */}
              <Box display="flex" justifyContent="center" mt={8}>
                <Button
                  type="submit"
                  bg="#d3401f"
                  color="#ffffff"
                  borderRadius="6px"
                  h="48px"
                  w="267px"
                  fontSize="18px"
                  fontWeight="600"
                  fontFamily="Inter"
                  loading={isSubmitting || startQuestionnaireMutation.isPending}
                  _hover={{ bg: "#b8351a" }}
                  _active={{ bg: "#a02e17" }}
                >
                  開始評估
                </Button>
              </Box>
            </VStack>
          </form>
        </Box>
      )}
    </Box>
  )
}
