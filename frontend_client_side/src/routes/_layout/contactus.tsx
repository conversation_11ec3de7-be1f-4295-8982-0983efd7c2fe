import { Stack, Flex } from "@chakra-ui/react"
import { createFileRoute } from "@tanstack/react-router"
import Header from '@/components/ui/header'
import { ChineseText } from "../../components/ui/fonts";
import { FaLocationDot } from "react-icons/fa6";
import { FaPhoneAlt } from "react-icons/fa";
import { MdEmail } from "react-icons/md";
import { GoogleMap, Marker } from '@react-google-maps/api';
import { useGoogleMaps } from "@/hooks/useGoogleMaps";
import { useSettings } from "@/hooks/useSettings";
const googleMapsApiKey = import.meta.env.VITE_GOOGLE_MAP_API_KEY


export const Route = createFileRoute('/_layout/contactus')({
    component: RouteComponent,
})

function RouteComponent() {
    const { data } = useSettings()


    const { isLoaded, loadError } = useGoogleMaps(googleMapsApiKey)

    return (
        <Stack
            w='full'
            justify={'center'}
            align={'center'}
        >
            <Header title={'聯絡我們'} />

            <Stack
                mt={'100px'}
                w='90%'>
                <ChineseText
                    fontSize='xl'
                    fontWeight="bold"
                    color='black'
                    w="50%"
                >
                    {"我們深知每個孩子的成長都獨一無二，您的每一次諮商都是對孩子未來的重視。無論您對評估服務有疑問、想預約專業測評，或希望合作推動兒童智慧發展，我們都將用心聆聽並提供支援！"}
                </ChineseText>
            </Stack>

            <Flex
                w='full'
                my={{base: '50px', md: '200px'}}
                direction={{ base: 'column', md: 'row' }}
            >
                <Stack
                    mt={{base: '50px', md: '0px'}}
                    id='contact-info'
                    ml={'5%'}
                    gap={2}
                    w={{base: 'full', md: '40%'}}
                    order={{ base: 2, md: 1 }}
                >
                    <Flex
                        align={'center'}
                    >
                        <FaLocationDot
                            size={'20px'}
                            color={'black'}
                        />
                        <ChineseText
                            ml={'15px'}
                            fontSize='md'
                            fontWeight="bold"
                            color='black'
                        >
                            {data?.address}
                        </ChineseText>
                    </Flex>
                    <Flex
                        align={'center'}
                    >
                        <FaPhoneAlt
                            size={'20px'}
                            color={'black'}
                        />
                        <ChineseText
                            ml={'15px'}
                            fontSize='md'
                            fontWeight="bold"
                            color='black'
                        >
                            {data?.phone}
                        </ChineseText>
                    </Flex>
                    <Flex
                        align={'center'}
                    >
                        <MdEmail
                            size={'20px'}
                            color={'black'}
                        />
                        <ChineseText
                            ml={'15px'}
                            fontSize='md'
                            fontWeight="bold"
                            color='black'
                        >
                            {data?.email}
                        </ChineseText>
                    </Flex>
                </Stack>

                <Stack
                    id='google-map'
                    w={{base: 'full', md: '60%'}}
                    order={{ base: 1, md: 2 }}
                >
                    {loadError ? (
                        <ChineseText fontSize='md' color='red.500'>
                            地圖加載失敗，請刷新頁面重試
                        </ChineseText>
                    ) : !isLoaded || !data?.latitude || !data?.longitude ? (
                        <ChineseText fontSize='md' color='gray.500'>
                            地圖位置正在加載中...
                        </ChineseText>
                    ) : (
                        <GoogleMap
                            mapContainerStyle={{
                                width: 'full',
                                height: '450px'
                            }}
                            zoom={18}
                            center={{
                                lat: data.latitude,
                                lng: data.longitude
                            }}
                        >
                            <Marker
                                key={"1"}
                                position={{
                                    lat: data.latitude,
                                    lng: data.longitude
                                }}
                            // icon={{ url: '/images/smalllogo.png', scaledSize: new window.google.maps.Size(40, 40) }} 
                            />
                        </GoogleMap>
                    )}
                </Stack>


            </Flex>

        </Stack>
    )
}
