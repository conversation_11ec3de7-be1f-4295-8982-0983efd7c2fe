import { Image, Stack } from "@chakra-ui/react"
import { createFileRoute, } from "@tanstack/react-router"
import { useQuery } from "@tanstack/react-query"
import { IntroductionService } from "@/client/sdk.gen"
import { ChineseText } from "../../components/ui/fonts";
const imageBaseUrl = import.meta.env.VITE_IMAGE_URL;
export const Route = createFileRoute("/_layout/")({
  component: Main,
})



const PER_PAGE = 20

function getItemsQueryOptions({ page }: { page: number }) {
  return {
    queryFn: () =>
      IntroductionService.readIntroductionList({ skip: (page - 1) * PER_PAGE, limit: PER_PAGE, }),
    queryKey: ["introductions", { page }],
  }
}

function Main() {
  const { data } = useQuery({
    ...getItemsQueryOptions({ page: 1 }),
    placeholderData: (prevData) => prevData,
  })

  const items = (data?.data.slice(0, PER_PAGE) ?? [])
    .sort((a, b) => a.index - b.index);


  console.log(items)

  return (

    <Stack maxW="full"
      h='auto'
      gap={0}
    >
      <Stack w='full'>
        <Image src="/images/main1.webp"
          alt="main"
          h={{ base: '70vh', sm: '70vh', md: '90vh', lg: '110vh' }}
          minH='500px'
          objectFit='cover'
          objectPosition="left"
          opacity={0.7} />
      </Stack>


      {
        items.length > 0 ? (items.map((item) => (
          <Stack
            justify={'flex-end'}
            w='full'
            h={{ base: '70vh', sm: '70vh', md: '90vh', lg: '110vh' }}
            minH='500px'
            position="relative"
            _before={{
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              backgroundImage: `url(${imageBaseUrl + item.image})`,
              backgroundSize: 'cover',
              backgroundPosition: 'left',
              backgroundRepeat: 'no-repeat',
              opacity: 0.7,
              zIndex: -1,
            }}
          >
            <Stack
              ml={5}
              mb={10}
              bgColor="rgba(255, 255, 255, 0.3)"
              maxW="50%"  // Change from w="50%" to maxW="50%"
              align="flex-start" // This ensures alignment
            >
              <Stack gap={0} maxW="fit-content">
                <ChineseText
                  id="mainTitle"
                  color='black'
                  fontSize='3xl'
                  fontWeight='bold'

                  w="auto" // Width based on content
                >
                  {item.title}
                </ChineseText>
                <ChineseText
                  id="mainDescription"
                  fontSize='xl'
                  whiteSpace="pre-line"
                  color='black'
                  maxW="100%" // Will never exceed the parent Stack width (which is determined by the title)
                >
                  {item.description}
                </ChineseText>
              </Stack>
            </Stack>
          </Stack>
        ))) : (<Stack />)
      }

    </Stack >

  )
}
