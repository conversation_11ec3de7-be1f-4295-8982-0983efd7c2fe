import { createFileRoute } from "@tanstack/react-router"
import { Stack, Flex, Box, Table, Button } from "@chakra-ui/react"
import { useState, useEffect } from "react"
import { useForm, type SubmitHandler } from "react-hook-form"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { ChineseText } from "../../components/ui/fonts"
import { FormInput } from "../../components/ui/formInput"
import { FormButton } from "../../components/ui/formButton"
import { Field } from "../../components/ui/field"
import { colors } from "../../colors"
import useAuth from "../../hooks/useAuth"
import Header from "../../components/ui/header"
import { ClientsService } from "../../client"
import type { ClientUpdateMe, ApiError } from "../../client"
import { handleError } from "../../utils"

export const Route = createFileRoute('/_layout/profile')({
  component: ProfilePage,
})

// Mock data for evaluation records
const mockEvaluationRecords = [
  {
    id: 1,
    date: "11/12/2024",
    type: "專心測",
    assessmentType: "小兒智能測評估",
    status: "查看結果"
  },
  {
    id: 2,
    date: "11/12/2024",
    type: "專心測",
    assessmentType: "小兒智能測評估",
    status: "查看結果"
  },
  {
    id: 3,
    date: "11/12/2024",
    type: "專心測",
    assessmentType: "小兒智能測評估",
    status: "查看結果"
  },
  {
    id: 4,
    date: "11/12/2024",
    type: "專心測",
    assessmentType: "小兒智能測評估",
    status: "查看結果"
  }
]

// Query function for current client
function getCurrentClientQueryOptions() {
  return {
    queryFn: () => ClientsService.readClientMe(),
    queryKey: ["currentClient"],
  }
}

interface ProfileFormData {
  full_name: string
  phone_number: string
  email: string
}

function ProfilePage() {
  const [editMode, setEditMode] = useState(false)
  const { logout } = useAuth()
  const queryClient = useQueryClient()

  // Fetch current client data
  const { data: currentUser, isLoading: isLoadingUser } = useQuery({
    ...getCurrentClientQueryOptions(),
    enabled: true, // Always fetch when component mounts
  })

  // Mutation for updating profile
  const updateProfileMutation = useMutation({
    mutationFn: (data: ClientUpdateMe) =>
      ClientsService.updateClientMe({ requestBody: data }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["currentClient"] })
      setEditMode(false)
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
  })

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ProfileFormData>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: {
      full_name: "",
      phone_number: "",
      email: "",
    },
  })

  // Reset form when user data is loaded
  useEffect(() => {
    if (currentUser) {
      reset({
        full_name: currentUser.full_name || "",
        phone_number: currentUser.phone_number || "",
        email: currentUser.email || "",
      })
    }
  }, [currentUser, reset])

  const toggleEditMode = () => {
    setEditMode(!editMode)
    if (!editMode) {
      // Reset form when entering edit mode
      reset({
        full_name: currentUser?.full_name || "",
        phone_number: currentUser?.phone_number || "",
        email: currentUser?.email || "",
      })
    }
  }

  const onSubmit: SubmitHandler<ProfileFormData> = async (data) => {
    // Check if email is being changed and if it already exists
    const updateData: ClientUpdateMe = {
      full_name: data.full_name || null,
      phone_number: data.phone_number || null,
      email: data.email || null,
    }

    updateProfileMutation.mutate(updateData)
  }

  const onCancel = () => {
    reset()
    setEditMode(false)
  }

  const handleCompleteRecords = () => {
    // TODO: Navigate to complete records page
    console.log("Navigate to complete records")
  }

  // Show loading state while fetching user data
  if (isLoadingUser) {
    return (
      <Stack w="full" minH="100vh" bg="#F5F5DC" gap={0}>
        <Header title="個人檔案" />
        <Flex w="full" justify="center" py={8} px={4} flex={1}>
          <ChineseText>載入中...</ChineseText>
        </Flex>
      </Stack>
    )
  }

  return (
    <Stack w="full" minH="100vh" bg="#F5F5DC" gap={0}>
      {/* Header */}
      <Header title="個人檔案" />

      {/* Main Content */}
      <Flex
        w="full"
        justify="center"
        py={8}
        px={4}
        flex={1}
      >
        <Flex
          w="90%"
          maxW="1200px"
          gap={8}
          direction={{ base: "column", lg: "row" }}
        >
          {/* Left Side - Personal Information */}
          <Box
            w={{ base: "full", lg: "45%" }}
            bg="white"
            p={6}
            borderRadius="lg"
            shadow="sm"
          >
            <ChineseText
              fontSize="xl"
              fontWeight="bold"
              mb={6}
              color="black"
            >
              個人資料
            </ChineseText>
            
            <Stack gap={4} as="form" onSubmit={handleSubmit(onSubmit)}>
              <Field
                label="姓名"
                invalid={!!errors.full_name}
                errorText={errors.full_name?.message}
              >
                {editMode ? (
                  <FormInput
                    {...register("full_name", {
                      required: "姓名為必填項目",
                      maxLength: { value: 50, message: "姓名不能超過50個字符" }
                    })}
                    placeholder="請輸入姓名"
                  />
                ) : (
                  <ChineseText color="black">
                    {currentUser?.full_name || "未設定"}
                  </ChineseText>
                )}
              </Field>

              <Field
                label="電話"
                invalid={!!errors.phone_number}
                errorText={errors.phone_number?.message}
              >
                {editMode ? (
                  <FormInput
                    {...register("phone_number", {
                      pattern: {
                        value: /^[0-9+\-\s()]+$/,
                        message: "請輸入有效的電話號碼"
                      }
                    })}
                    placeholder="請輸入電話號碼"
                  />
                ) : (
                  <ChineseText color="black">
                    {currentUser?.phone_number || "未設定"}
                  </ChineseText>
                )}
              </Field>

              <Field
                label="電郵"
                invalid={!!errors.email}
                errorText={errors.email?.message}
              >
                {editMode ? (
                  <FormInput
                    {...register("email", {
                      required: "電郵為必填項目",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "請輸入有效的電郵地址"
                      }
                    })}
                    placeholder="請輸入電郵地址"
                    type="email"
                  />
                ) : (
                  <ChineseText color="black">
                    {currentUser?.email || "未設定"}
                  </ChineseText>
                )}
              </Field>

              <Flex gap={3} mt={4}>
                {editMode ? (
                  <>
                    <FormButton
                      type="submit"
                      loading={updateProfileMutation.isPending}
                      w="auto"
                      px={6}
                    >
                      保存
                    </FormButton>
                    <Button
                      variant="outline"
                      onClick={onCancel}
                      disabled={updateProfileMutation.isPending}
                      w="auto"
                      px={6}
                      borderColor={colors.mainColor}
                      color={colors.mainColor}
                      _hover={{
                        bg: colors.mainColor,
                        color: "white"
                      }}
                    >
                      <ChineseText>取消</ChineseText>
                    </Button>
                  </>
                ) : (
                  <FormButton
                    type="button"
                    onClick={toggleEditMode}
                    w="auto"
                    px={6}
                  >
                    修改
                  </FormButton>
                )}
              </Flex>
            </Stack>
          </Box>

          {/* Right Side - Evaluation Records */}
          <Stack
            w={{ base: "full", lg: "55%" }}
            gap={4}
          >
            <Box
              bg="white"
              p={6}
              borderRadius="lg"
              shadow="sm"
            >
              <ChineseText
                fontSize="xl"
                fontWeight="bold"
                mb={6}
                color="black"
              >
                評估記錄
              </ChineseText>
              
              <Box overflowX="auto">
                <Table.Root size="sm">
                  <Table.Header>
                    <Table.Row>
                      <Table.ColumnHeader>
                        <ChineseText fontWeight="bold" color="black">評估日期</ChineseText>
                      </Table.ColumnHeader>
                      <Table.ColumnHeader>
                        <ChineseText fontWeight="bold" color="black">評估類型</ChineseText>
                      </Table.ColumnHeader>
                      <Table.ColumnHeader>
                        <ChineseText fontWeight="bold" color="black">智能核心評估類型</ChineseText>
                      </Table.ColumnHeader>
                      <Table.ColumnHeader>
                        <ChineseText fontWeight="bold" color="black">狀態</ChineseText>
                      </Table.ColumnHeader>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {mockEvaluationRecords.map((record) => (
                      <Table.Row key={record.id}>
                        <Table.Cell>
                          <ChineseText color="black">{record.date}</ChineseText>
                        </Table.Cell>
                        <Table.Cell>
                          <ChineseText color="black">{record.type}</ChineseText>
                        </Table.Cell>
                        <Table.Cell>
                          <ChineseText color="black">{record.assessmentType}</ChineseText>
                        </Table.Cell>
                        <Table.Cell>
                          <Button
                            size="sm"
                            variant="ghost"
                            color={colors.mainColor}
                            p={0}
                            h="auto"
                            fontWeight="normal"
                          >
                            <ChineseText color={colors.mainColor}>
                              {record.status}
                            </ChineseText>
                          </Button>
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table.Root>
              </Box>
              <Flex justify="flex-end" mt={4}>
                <Button
                 
                  onClick={handleCompleteRecords}
                  color={colors.mainColor}
                >
                  <ChineseText>查看完整記錄</ChineseText>
                </Button>
              </Flex>
            </Box>
            <Flex
              gap={4}
              justify="flex-start"
            >
              <Button
                onClick={logout}
                w="auto"
                px={8}
                bg="red.500"
                color="white"
                _hover={{
                  bg: "red.600",
                }}
              >
                <ChineseText color="white">登出</ChineseText>
              </Button>
            </Flex>
          </Stack>
        </Flex>
      </Flex>
    </Stack>
  )
}

