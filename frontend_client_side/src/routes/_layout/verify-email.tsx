import { createFile<PERSON><PERSON><PERSON>, <PERSON>, useSearch, useNavigate } from '@tanstack/react-router'
import { Stack, Flex, Image, Button } from "@chakra-ui/react"
import { useMutation } from '@tanstack/react-query'
import { ChineseText } from "../../components/ui/fonts";
import { ClientsService } from '../../client/sdk.gen'
import { colors } from "@/colors"
import { useEffect, useState } from 'react'
import { MdEmail, MdMarkEmailRead } from "react-icons/md";

type VerifyEmailSearchParams = {
  token?: string
}

export const Route = createFileRoute('/_layout/verify-email')({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>): VerifyEmailSearchParams => {
    return {
      token: search.token as string | undefined
    }
  }
})

function RouteComponent() {
  const { token } = useSearch({ from: '/_layout/verify-email' })

  const verifyEmailMutation = useMutation({
    mutationFn: (token: string) => ClientsService.verifyEmail({ token })
  })

  useEffect(() => {
    if (token && !verifyEmailMutation.isPending && !verifyEmailMutation.isSuccess) {
      verifyEmailMutation.mutate(token)
    }
  }, [token])

  const navigate = useNavigate()
  const [countdown, setCountdown] = useState(3)

  useEffect(() => {
    let timeoutId: number
    let intervalId: number

    if (verifyEmailMutation.isSuccess) {
      intervalId = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(intervalId)
            return 0
          }
          return prev - 1
        })
      }, 1000)

      timeoutId = setTimeout(() => {
        navigate({ to: '/signin' })
      }, 3000)
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId)
      if (intervalId) clearInterval(intervalId)
    }
  }, [verifyEmailMutation.isSuccess, navigate])

  return (
    <Stack direction="column" gap={3} mx={'auto'} my="100px" maxW="5xl" py={12} px={6} bgColor='white' rounded={'3xl'}>

      {!token ? (
        <Stack align={'center'} my="50px">
          <Stack w='100px' h='100px' mx='auto' rounded={'100px'} bgColor={colors.mainColor} justify={'center'} align={'center'}>
            <MdEmail size={50} color={'white'} />
          </Stack>
          <ChineseText fontSize="2xl" fontWeight="bold" color={colors.mainColor} mt={'20px'}>
            {"很快完成了，還差最後一步！"}
          </ChineseText>
          <ChineseText color={'gray.600'}>
            {"我們已經發送了一封驗證郵件到您的郵箱，請點擊郵件中的鏈接驗證您的郵箱。"}
          </ChineseText>
          <Link to="/">
            <Button bgColor={colors.mainColor} color="white" mt={4}
              _hover={{
                borderColor: 'white',
                bg: colors.mainColor,
                opacity: 0.8,
              }}
              _active={{
                bg: colors.mainColor,
                borderWidth: '0px',
                opacity: 0.8,
              }}
              _focus={{
                outline: 'none',
                boxShadow: 'none',
                borderWidth: '0px',
                borderColor: 'transparent',
              }}
            >
              {"返回主頁"}
            </Button>
          </Link>
        </Stack>
      ) : verifyEmailMutation.isPending ? (
        <ChineseText>Verifying your email...</ChineseText>
      ) : verifyEmailMutation.isSuccess ? (
        <Stack gap={4} align={'center'}>
          <Stack w='100px' h='100px' mx='auto' rounded={'100px'} bgColor={colors.mainColor} justify={'center'} align={'center'}>
            <MdMarkEmailRead size={50} color={'white'} />
          </Stack>
          <ChineseText fontSize="xl" color={colors.mainColor}>
            {"驗證成功"}
          </ChineseText>
          <ChineseText color={'gray.600'}>
            {"恭喜🎉，您已成功驗證您的郵箱！"}
          </ChineseText>
          <ChineseText color={'gray.600'}>
            {`${countdown}秒後自動跳轉至登入頁面`}
          </ChineseText>
        </Stack>
      ) : (
        <Stack gap={4} align={'center'}>
          <ChineseText fontSize="xl" color={colors.mainColor}>
            Email verified Failed!
          </ChineseText>
          <Link to="/signin">
            <Button
              colorScheme="blue"
              variant="solid"
              mt={4}
            >
              Go to Login
            </Button>
          </Link>
        </Stack>
      )}

    </Stack>
  )
}
