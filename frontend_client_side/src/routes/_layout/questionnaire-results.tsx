import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Grid,
  GridItem,
  Circle,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useNavigate } from "@tanstack/react-router"

import { QuestionnairesService } from "@/client"

export const Route = createFileRoute("/_layout/questionnaire-results")({
  component: QuestionnaireResultsPage,
})

// Mock results data - in real app this would come from API
const mockResults = {
  childName: "小明",
  surveyType: "3-6歲評估",
  completedAt: "2025年1月12日",
  totalQuestions: 97,
  answeredQuestions: 97,
  intelligenceCategories: [
    {
      name: "語言智能",
      score: 85,
      level: "優秀",
      description: "孩子在語言表達、詞彙理解和溝通能力方面表現出色",
      color: "#4CAF50"
    },
    {
      name: "邏輯數學智能",
      score: 78,
      level: "良好",
      description: "具備良好的數理邏輯思維和問題解決能力",
      color: "#2196F3"
    },
    {
      name: "空間智能",
      score: 92,
      level: "卓越",
      description: "在空間認知、視覺想像和藝術創作方面天賦突出",
      color: "#9C27B0"
    },
    {
      name: "身體動覺智能",
      score: 73,
      level: "良好",
      description: "身體協調性和運動技能發展正常",
      color: "#FF9800"
    },
    {
      name: "音樂智能",
      score: 88,
      level: "優秀",
      description: "對音樂節拍、旋律和聲音有很好的感知能力",
      color: "#E91E63"
    },
    {
      name: "人際智能",
      score: 80,
      level: "良好",
      description: "能夠理解他人情感，具備良好的社交能力",
      color: "#00BCD4"
    },
    {
      name: "內省智能",
      score: 75,
      level: "良好",
      description: "對自我認知和情緒管理有一定的理解",
      color: "#795548"
    },
    {
      name: "自然觀察智能",
      score: 82,
      level: "優秀",
      description: "對自然環境和生物有敏銳的觀察力",
      color: "#4CAF50"
    }
  ],
  overallAssessment: "您的孩子在多個智能領域都表現出良好的發展潛力，特別在空間智能和音樂智能方面表現卓越。建議繼續培養這些優勢領域，同時也要關注其他智能的均衡發展。",
  recommendations: [
    "鼓勵孩子參與更多藝術創作活動，如繪畫、手工製作等",
    "提供音樂學習機會，如學習樂器或參加音樂活動",
    "通過遊戲和實際操作來加強數學邏輯思維",
    "增加戶外活動和自然探索的機會",
    "培養孩子的閱讀習慣，豐富語言表達能力"
  ]
}

const getScoreColor = (score: number) => {
  if (score >= 90) return "#4CAF50" // Green - 卓越
  if (score >= 80) return "#2196F3" // Blue - 優秀  
  if (score >= 70) return "#FF9800" // Orange - 良好
  if (score >= 60) return "#FFC107" // Yellow - 一般
  return "#F44336" // Red - 需要關注
}

const getScoreLevel = (score: number) => {
  if (score >= 90) return "卓越"
  if (score >= 80) return "優秀"
  if (score >= 70) return "良好"
  if (score >= 60) return "一般"
  return "需要關注"
}

function QuestionnaireResultsPage() {
  const navigate = useNavigate()

  // Get questionnaire results - in real app this would fetch from API
  const { isLoading } = useQuery({
    queryKey: ["my-questionnaire-results"],
    queryFn: () => QuestionnairesService.getMyAvailableQuestionnaire(),
    enabled: false, // Disabled for now, using mock data
  })

  if (isLoading) {
    return (
      <Box bg="#f9f5e9" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <Text fontSize="24px" fontFamily="Inter">載入結果中...</Text>
      </Box>
    )
  }

  return (
    <Box bg="#f9f5e9" minH="100vh" py={8}>
      <Box maxW="1200px" mx="auto" px={8}>
        <VStack gap={8} align="stretch">
          {/* Header Section */}
          <Box textAlign="center" py={6}>
            <Text
              fontSize="48px"
              fontWeight="bold"
              color="#000000"
              fontFamily="Inter"
              mb={4}
            >
              🎉 評估完成！
            </Text>
            <Text
              fontSize="24px"
              color="#666666"
              fontFamily="Inter"
              mb={2}
            >
              {mockResults.childName} 的智能核心評估報告
            </Text>
            <Text
              fontSize="18px"
              color="#888888"
              fontFamily="Inter"
            >
              評估類型：{mockResults.surveyType} | 完成時間：{mockResults.completedAt}
            </Text>
            <Text
              fontSize="16px"
              color="#888888"
              fontFamily="Inter"
              mt={2}
            >
              已完成 {mockResults.answeredQuestions}/{mockResults.totalQuestions} 題
            </Text>
          </Box>

          {/* Overall Assessment */}
          <Box
            bg="#ffffff"
            borderRadius="16px"
            p={6}
            boxShadow="0 4px 12px rgba(0,0,0,0.1)"
          >
            <Text
              fontSize="24px"
              fontWeight="bold"
              color="#000000"
              fontFamily="Inter"
              mb={4}
            >
              📊 整體評估
            </Text>
            <Text
              fontSize="18px"
              color="#333333"
              fontFamily="Inter"
              lineHeight="1.6"
            >
              {mockResults.overallAssessment}
            </Text>
          </Box>

          {/* Intelligence Categories Results */}
          <Box
            bg="#ffffff"
            borderRadius="16px"
            p={6}
            boxShadow="0 4px 12px rgba(0,0,0,0.1)"
          >
            <Text
              fontSize="24px"
              fontWeight="bold"
              color="#000000"
              fontFamily="Inter"
              mb={6}
            >
              🧠 各項智能表現
            </Text>
            
            <Grid templateColumns="repeat(auto-fit, minmax(300px, 1fr))" gap={6}>
              {mockResults.intelligenceCategories.map((category, index) => (
                <GridItem key={index}>
                  <Box
                    border="1px solid #e0e0e0"
                    borderRadius="12px"
                    p={4}
                    _hover={{ boxShadow: "0 2px 8px rgba(0,0,0,0.1)" }}
                    transition="all 0.2s"
                  >
                    <HStack justify="space-between" mb={3}>
                      <Text
                        fontSize="18px"
                        fontWeight="bold"
                        color="#000000"
                        fontFamily="Inter"
                      >
                        {category.name}
                      </Text>
                      <HStack>
                        <Circle
                          size="40px"
                          bg={getScoreColor(category.score)}
                          color="white"
                          fontSize="14px"
                          fontWeight="bold"
                        >
                          {category.score}
                        </Circle>
                        <Text
                          fontSize="14px"
                          fontWeight="bold"
                          color={getScoreColor(category.score)}
                        >
                          {getScoreLevel(category.score)}
                        </Text>
                      </HStack>
                    </HStack>
                    
                    {/* Score Bar */}
                    <Box
                      bg="#f0f0f0"
                      borderRadius="10px"
                      h="8px"
                      mb={3}
                      overflow="hidden"
                    >
                      <Box
                        bg={getScoreColor(category.score)}
                        h="100%"
                        w={`${category.score}%`}
                        borderRadius="10px"
                        transition="width 0.5s ease"
                      />
                    </Box>
                    
                    <Text
                      fontSize="14px"
                      color="#666666"
                      fontFamily="Inter"
                      lineHeight="1.4"
                    >
                      {category.description}
                    </Text>
                  </Box>
                </GridItem>
              ))}
            </Grid>
          </Box>

          {/* Recommendations */}
          <Box
            bg="#ffffff"
            borderRadius="16px"
            p={6}
            boxShadow="0 4px 12px rgba(0,0,0,0.1)"
          >
            <Text
              fontSize="24px"
              fontWeight="bold"
              color="#000000"
              fontFamily="Inter"
              mb={4}
            >
              💡 發展建議
            </Text>
            <VStack align="stretch" gap={3}>
              {mockResults.recommendations.map((recommendation, index) => (
                <HStack key={index} align="flex-start">
                  <Circle size="24px" bg="#d3401f" color="white" fontSize="12px" fontWeight="bold" mt={1}>
                    {index + 1}
                  </Circle>
                  <Text
                    fontSize="16px"
                    color="#333333"
                    fontFamily="Inter"
                    lineHeight="1.5"
                    flex={1}
                  >
                    {recommendation}
                  </Text>
                </HStack>
              ))}
            </VStack>
          </Box>

          {/* Action Buttons */}
          <HStack justify="center" gap={4} pt={4}>
            <Button
              bg="#d3401f"
              color="#ffffff"
              borderRadius="8px"
              h="48px"
              px="32px"
              fontSize="18px"
              fontWeight="600"
              fontFamily="Inter"
              onClick={() => window.print()}
              _hover={{ bg: "#b8351a" }}
            >
              📄 列印報告
            </Button>
            <Button
              bg="#2196F3"
              color="#ffffff"
              borderRadius="8px"
              h="48px"
              px="32px"
              fontSize="18px"
              fontWeight="600"
              fontFamily="Inter"
              onClick={() => navigate({ to: "/" })}
              _hover={{ bg: "#1976D2" }}
            >
              🏠 返回首頁
            </Button>
          </HStack>
        </VStack>
      </Box>
    </Box>
  )
}
