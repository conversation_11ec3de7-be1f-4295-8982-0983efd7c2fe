import { createFileRoute } from '@tanstack/react-router'
import { Image, Box, Stack, Flex, Button } from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { MaCourseService } from "@/client/sdk.gen"
import { colors } from '../../colors'
import useEmblaCarousel from 'embla-carousel-react'
import { useCallback, useEffect, useState } from 'react'
import { ChineseText } from "../../components/ui/fonts";
const imageBaseUrl = import.meta.env.VITE_IMAGE_URL;
export const Route = createFileRoute('/_layout/ma')({
    component: Ma,
})

const PER_PAGE = 20



function getImagesQueryOptions({ page }: { page: number }) {
    return {
        queryFn: () =>
            MaCourseService.getMacourses({ skip: (page - 1) * PER_PAGE, limit: PER_PAGE, }),
        queryKey: ["introductions", { page }],
    }
}


function Ma() {

    // Initialize Embla Carousel
    const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true })
    const [selectedIndex, setSelectedIndex] = useState(0)

    const { data } = useQuery({
        ...getImagesQueryOptions({ page: 1 }),
        placeholderData: (prevData) => prevData,
    })


    const items = (data?.data.slice(0, PER_PAGE) ?? [])
        .sort((a, b) => a.index - b.index);


    // Update the selected index when the slide changes
    const onSelect = useCallback(() => {
        if (!emblaApi) return
        setSelectedIndex(emblaApi.selectedScrollSnap())
    }, [emblaApi])

    // Scroll to slide when a dot is clicked
    const scrollTo = useCallback(
        (index: number) => emblaApi && emblaApi.scrollTo(index),
        [emblaApi]
    )

    useEffect(() => {
        if (!emblaApi) return
        onSelect()
        emblaApi.on('select', onSelect)
        return () => {
            emblaApi.off('select', onSelect)
        }
    }, [emblaApi, onSelect])


    return (
        <Stack
            w='full'
            h='auto'
            align={'center'}
        >
            {/* Embla Carousel wrapper */}
            <Box className="embla" overflow="hidden" ref={emblaRef} w='90vw' mt={5}>
                <Box className="embla__container" display="flex">
                    {items.map((item) => (
                        <Box
                            key={item.id}
                            className="embla__slide"
                            flex="0 0 100%"
                            minWidth="0"
                        >
                            <Image
                                src={`${imageBaseUrl}${item.image}`}
                                w="100%"
                                h="auto"
                            />
                        </Box>
                    ))}
                </Box>
            </Box>

            {/* Dots */}
            <Flex justify="center" align={'center'} mt={4}>
                {items.map((_, index) => (
                    <Flex
                        key={index}
                        onClick={() => scrollTo(index)}
                        minWidth="5px"
                        minHeight="5px"
                        width={index === selectedIndex ? "10px" : "5px"}
                        height={index === selectedIndex ? "10px" : "5px"}
                        padding="0"
                        borderRadius="50%"
                        mx={1}
                        bg={colors.mainColor}

                    />
                ))}
            </Flex>

            <Flex mt={5} w='90vw'>
                <ChineseText fontSize="2xl" fontWeight="bold" color={colors.mainColor}
                    _hover={{
                        textDecoration: 'underline',
                        //transform: 'scale(1.05)'
                    }}
                    onClick={() => {
                        //window.open('https://wa.me/85212345678', '_blank');
                    }}>
                    {"按此Whatsapp我們"}
                </ChineseText>
            </Flex>
        </Stack>
    )
}