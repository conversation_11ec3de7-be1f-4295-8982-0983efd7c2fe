/* Baskervville Font */
@font-face {
  font-family: 'Baskervville';
  src: url('/fonts/Baskervville/Baskervville-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Baskervville';
  src: url('/fonts/Baskervville/Baskervville-Italic.ttf') format('truetype');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

/* Merriweather Font */
@font-face {
  font-family: 'Merriweather';
  src: url('/fonts/Merriweather/Merriweather-VariableFont_opsz,wdth,wght.ttf') format('truetype-variations');
  font-weight: 300 900; /* Light to Black */
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Merriweather';
  src: url('/fonts/Merriweather/Merriweather-Italic-VariableFont_opsz,wdth,wght.ttf') format('truetype-variations');
  font-weight: 300 900; /* Light to Black */
  font-style: italic;
  font-display: swap;
}

/* Noto Sans TC Font (Traditional Chinese) */
@font-face {
  font-family: 'Noto Sans TC';
  src: url('/fonts/Noto_Sans_TC/NotoSansTC-VariableFont_wght.ttf') format('truetype-variations');
  font-weight: 100 900; /* Thin to Black */
  font-style: normal;
  font-display: swap;
}

:root {
  /* Font family variables */
  --font-serif: 'Baskervville', Georgia, serif;
  --font-serif-literary: 'Merriweather', Georgia, serif;
  --font-sans-tc: 'Noto Sans TC', sans-serif;
  --font-sans: system-ui, Avenir, Helvetica, Arial, sans-serif;

  /* Color variables */

  --color-mainColor: #D3401F


  /* Default font settings */
  font-family: var(--font-sans);
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #F9F5E9;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
