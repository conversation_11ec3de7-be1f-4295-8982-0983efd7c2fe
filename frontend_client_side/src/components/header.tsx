import { Button, Flex, Text, Box, useDisclosure, Link } from "@chakra-ui/react";
import { ChineseText } from "./ui/fonts";

const Header = ({ title }: { title: string }) => {
    return (
        <Flex
            w="full"
            h="150px"
            align="center"
            justify={"center"}
            position="relative"
            overflow="hidden"
        >
            {/* Background image with opacity */}
            <Box
                position="absolute"
                top={0}
                left={0}
                right={0}
                bottom={0}
                backgroundImage="url('/images/background.jpeg')"
                backgroundSize="cover"
                backgroundPosition="center"
                backgroundRepeat="no-repeat"
                opacity={0.5}
                zIndex={0}
            />

            {/* Content overlay */}
            <Box position="relative" zIndex={1}>
                <Flex
                    as="nav"
                    align="center"
                    justify="space-between"
                    w='90vw'
                    h="80px"
                >
                    <ChineseText
                        color="black"
                        fontSize={{ base: "xl", sm: "xl", md: "2xl", lg: "2xl" }}
                        fontWeight="bold"
                    >
                        {title}
                    </ChineseText>
                </Flex>
            </Box>
        </Flex>
    );
};

export default Header;
