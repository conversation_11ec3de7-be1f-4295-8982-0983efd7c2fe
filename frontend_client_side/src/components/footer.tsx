import { Flex, Text, Stack, Image, Link } from "@chakra-ui/react";

import { Link as RouterLink } from "@tanstack/react-router";


import { colors } from '../colors'
import { ChineseText } from "./ui/fonts";

const mainPages = [
    {
        name: "主頁",
        path: "/",
    },
    {
        name: "智能核心評估",
        path: "/",
    },
    {
        name: "MA心理動機評估",
        path: "/",
    },
    {
        name: "課程及活動",
        path: "/",
    },
]

const subPages = [
    {
        name: "關於我們",
        path: "/",
    },
    {
        name: "聯絡我們",
        path: "/",
    },
]

const meida = [
    {
        name: "Facebook",
        link: "/",
    },
    {
        name: "Whatsapp",
        link: "/",
    },
]

const footerIntro = `專注兒童智能心理評估領域
為3-15歲兒童提供科學的發展潛能評估
心理健康篩檢及個人化成長建議幫助家長精準掌握兒童認知發展
情緒特質與學習優勢`

import { useSettings } from "@/hooks/useSettings";

export const Footer = () => {
    const { data } = useSettings();

    return (
        <Stack
            w='full'
            h={'auto'}
            align={'center'}
        >
            <Flex
                w='90%'
                h='60px'

            >

            </Flex>

            <Flex
                direction={{ base: 'column', sm: 'column', md: 'row', lg: 'row' }}
                w='90%'
                h={{ sm: '400px', md: '250px', lg: '250px' }}
            >
                <Flex
                    w={{ base: '100%', sm: '100%', md: '100%', lg: '50%' }}
                    justify={{ base: 'center', sm: 'center', md: 'center', lg: 'flex-start' }}
                >
                    <Image src='/images/logo.webp' alt='logo' maxW='110px' fit={'contain'} />
                    <Stack
                        gap={5}
                        justify={'center'}
                    >
                        <Text
                            fontSize={{ base: 'md', sm: 'lg', md: 'lg', lg: 'lg' }}
                            fontWeight={'bold'}
                            color={'black'}
                        >
                            {"Decoding Happiness"}
                        </Text>
                        <ChineseText
                            color="black"
                            lineHeight={1.5}
                            fontSize={{ base: 'sm', sm: 'md', md: 'md', lg: 'md' }}
                            textAlign={{ sm: 'left', md: 'left' }}
                            whiteSpace="pre-line" // This preserves line breaks
                        >
                            {footerIntro}
                        </ChineseText>

                    </Stack>

                </Flex>

                <Flex
                    w={{ base: '100%', sm: '100%', md: '100%', lg: '50%' }}
                >
                    <Flex
                        w='100%'
                        mt={10}
                        gap={{ base: '8', sm: '12', md: '10', lg: '10', xl: '20' }}
                        justify={{ base: 'center', sm: 'center', md: 'flex-end', lg: 'flex-end' }}


                    >
                        <Stack
                            gap={5}
                        >
                            {
                                mainPages.map((item) => (
                                    <RouterLink to={item.path} key={item.name}>
                                        <ChineseText
                                            color="black"
                                            fontSize={{ base: 'sm', sm: 'md', md: 'md', lg: 'md' }}
                                        >
                                            {item.name}
                                        </ChineseText>
                                    </RouterLink>
                                ))
                            }
                        </Stack>

                        <Stack
                            gap={5}
                        >
                            {
                                subPages.map((item) => (
                                    <RouterLink to={item.path} key={item.name}>
                                        <ChineseText
                                            color="black"
                                            fontSize={{ base: 'sm', sm: 'md', md: 'md', lg: 'md' }}

                                        >
                                            {item.name}
                                        </ChineseText>
                                    </RouterLink>
                                ))
                            }
                        </Stack>

                        <Stack
                            gap={5}>
                            {
                                meida.map((item) => (
                                    <Link 
                                        href={item.name === 'Facebook' ? data?.facebook ?? '#' : data?.whatsapp ?? '#'} 
                                        key={item.name} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                    >
                                        <ChineseText
                                            color={colors.mainColor}
                                            fontSize={{ base: 'sm', sm: 'md', md: 'md', lg: 'md' }}
                                        >
                                            {item.name}
                                        </ChineseText>
                                    </Link>
                                ))
                            }
                        </Stack>

                    </Flex>

                </Flex>

            </Flex>

            <Flex
                w='90%'

                h='60px'
                borderTopWidth={1}
                justify={'center'}
                align={'center'}
                gap={2}
            >

                <RouterLink to={'/'} >
                    <ChineseText
                        color={'black'}
                        fontSize="md"
                    >
                        {'隱私權政策'}
                    </ChineseText>
                </RouterLink>

                <Text color={'black'}>{"|"}</Text>

                <RouterLink to={'/'} >
                    <ChineseText
                        color={'black'}
                        fontSize="md"
                    >
                        {'條款與條件'}
                    </ChineseText>
                </RouterLink>

            </Flex>
        </Stack>
    )
}

export default Footer;
