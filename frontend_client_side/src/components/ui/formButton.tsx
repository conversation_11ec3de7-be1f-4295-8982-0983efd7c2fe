import { Button, ButtonProps } from "@chakra-ui/react";
import { ReactNode } from "react";
import { colors } from "@/colors";

interface FormButtonProps extends ButtonProps {
    children: ReactNode;
}

export const FormButton = ({ children, ...props }: FormButtonProps) => {
    return (
        <Button
            w = 'full'
            fontFamily={'var(--font-sans-tc)'}
            bgColor={colors.mainColor}
            color={'white'}
            _hover={{
                borderColor: 'white',
                bg: colors.mainColor,
                opacity: 0.8,
            }}
            _active={{
                bg: colors.mainColor,
                borderWidth: '0px',
                opacity: 0.8,
            }}
            _focus={{
                outline: 'none',
                boxShadow: 'none',
                borderWidth: '0px',
                borderColor: 'transparent',
            }}
            {...props}
        >
            {children}
        </Button>
    );
};
