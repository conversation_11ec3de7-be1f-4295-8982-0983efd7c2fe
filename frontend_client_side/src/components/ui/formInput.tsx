import { Input as ChakraInput, InputProps as ChakraInputProps } from "@chakra-ui/react";
import { UseFormRegisterReturn } from "react-hook-form";

interface InputProps extends ChakraInputProps {
  registration?: UseFormRegisterReturn;
}

export const FormInput = ({ registration, ...props }: InputProps) => {
  return (
    <ChakraInput
      borderColor={'gray.300'}
      rounded={'lg'}
      bgColor={'white'}
      {...registration}
      {...props}
    />
  );
};
