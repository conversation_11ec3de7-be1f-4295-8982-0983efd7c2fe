import { Text, Heading, TextProps, HeadingProps } from "@chakra-ui/react";
import React from "react";

// Utility components for using the custom fonts

export const SerifText = (props: TextProps) => {
  const { style, ...rest } = props;
  return (
    <Text 
      {...rest} 
      style={{ 
        fontFamily: "var(--font-serif)",
        ...style 
      }} 
    />
  );
};

export const SerifLiteraryText = (props: TextProps) => {
  const { style, ...rest } = props;
  return (
    <Text 
      {...rest} 
      style={{ 
        fontFamily: "var(--font-serif-literary)",
        ...style 
      }} 
    />
  );
};

export const ChineseText = (props: TextProps) => {
  const { style, ...rest } = props;
  return (
    <Text 
      {...rest} 
      style={{ 
        fontFamily: "var(--font-sans-tc)",
        ...style 
      }} 
    />
  );
};
export const SerifHeading = (props: HeadingProps) => {
  const { style, ...rest } = props;
  return (
    <Heading 
      {...rest} 
      style={{ 
        fontFamily: "var(--font-serif)",
        ...style  
      }} 
    />
  );
};

export const LiteraryHeading = (props: HeadingProps) => {
  const { style, ...rest } = props;
  return (
    <Heading 
      {...rest} 
      style={{ 
        fontFamily: "var(--font-serif-literary)",
        ...style 
      }} 
    />
  );
};

// Higher-order component to apply custom font to any component
export const withFont = (
  Component: React.ComponentType<any>,
  fontFamily: string,
  defaultWeight?: string | number
) => {
  return (props: any) => {
    const { style, fontWeight, ...rest } = props;
    return (
      <Component 
        {...rest} 
        fontWeight={fontWeight || defaultWeight}
        style={{ 
          fontFamily,
          ...style 
        }} 
      />
    );
  };
};

// Usage examples:
// const BaskervvilleText = withFont(Text, "var(--font-serif)");
// const BoldMerriweatherHeading = withFont(Heading, "var(--font-serif-literary)", "bold");
// const SemiBoldChineseButton = withFont(Button, "var(--font-sans-tc)", 600);
