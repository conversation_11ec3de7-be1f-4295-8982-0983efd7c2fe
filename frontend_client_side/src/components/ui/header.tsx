import { Flex, Text, Stack, Image } from "@chakra-ui/react";
import { ChineseText } from "./fonts";

function Header({ title }: { title: string }) {
    return (
        <Flex
            w='full'
        >
            <Flex
                w={'30%'}
                ml={'5%'}
                align={'flex-end'}
            >
                <ChineseText
                    color='black'
                    fontSize={{ base: 'xl', sm: 'xl', md: '2xl', lg: '4xl' }}
                    fontWeight='bold'
                >
                    {title}
                </ChineseText>
            </Flex>
            <Image
                src='/images/main1.webp'
                w='70%'
                fit='cover'
                // maxH={{ base: '300px', sm: '300px', md: '400px', lg: '600px' }}
                opacity={0.7}
                objectPosition='bottom'
            />

        </Flex>
    )
}

export default Header