import { Stack, StackProps } from "@chakra-ui/react";
import { forwardRef } from "react";import { motion, isValidMotionProp } from "framer-motion";

export const MotionStack = motion(
  forwardRef<HTMLDivElement, StackProps>((props, ref) => {
    const chakraProps = Object.fromEntries(
      Object.entries(props).filter(([key]) => !isValidMotionProp(key))
    );
    return <Stack ref={ref} {...chakraProps} />;
  })
);
