import { Box, Stack, Text, Heading, Button } from "@chakra-ui/react";
import { 
  SerifText, 
  SerifLiteraryText, 
  ChineseText, 
  SerifHeading,
  LiteraryHeading,
  withFont
} from "./fonts";

// Example of using the HOC with a Button
const ChineseButton = withFont(Button, "var(--font-sans-tc)");
// Example with default font weight
const BoldChineseButton = withFont(Button, "var(--font-sans-tc)", "bold");

export const FontDemo = () => {
  return (
    <Stack direction="column" gap="6" alignItems="start" width="100%" p="4">
      <Box>
        <Heading size="lg" mb={2}>Font Usage Examples</Heading>
        <Text>This is regular Chakra UI Text with default font</Text>
      </Box>

      <Box as="hr" borderTop="1px solid" borderColor="gray.200" width="100%" my={2} />

      <Box width="100%">
        <SerifHeading size="md" mb={4}>Baskervville Font (Serif)</SerifHeading>
        
        <Stack direction="column" gap={3}>
          <SerifText fontWeight="normal">
            Normal weight - This text uses the Baskervville font with normal weight.
          </SerifText>
          
          <SerifText fontWeight="bold">
            Bold weight - This text uses the Baskervville font with bold weight.
          </SerifText>
          
          <SerifText as="div">
            <Text as="span" fontWeight="normal">Normal</Text> and 
            <Text as="span" fontWeight="bold" ml={2}>Bold</Text> weights in the same line.
          </SerifText>
        </Stack>
      </Box>

      <Box as="hr" borderTop="1px solid" borderColor="gray.200" width="100%" my={2} />

      <Box width="100%">
        <LiteraryHeading size="md" mb={4}>Merriweather Font (Serif Literary)</LiteraryHeading>
        
        <Stack direction="column" gap={3}>
          <SerifLiteraryText fontWeight="light">
            Light weight (300) - This text uses Merriweather with light weight.
          </SerifLiteraryText>
          
          <SerifLiteraryText fontWeight="normal">
            Normal weight (400) - This text uses Merriweather with normal weight.
          </SerifLiteraryText>
          
          <SerifLiteraryText fontWeight="medium">
            Medium weight (500) - This text uses Merriweather with medium weight.
          </SerifLiteraryText>
          
          <SerifLiteraryText fontWeight="semibold">
            Semibold weight (600) - This text uses Merriweather with semibold weight.
          </SerifLiteraryText>
          
          <SerifLiteraryText fontWeight="bold">
            Bold weight (700) - This text uses Merriweather with bold weight.
          </SerifLiteraryText>
          
          <SerifLiteraryText fontWeight="extrabold">
            Extrabold weight (800) - This text uses Merriweather with extrabold weight.
          </SerifLiteraryText>
          
          <SerifLiteraryText fontWeight="black">
            Black weight (900) - This text uses Merriweather with black weight.
          </SerifLiteraryText>
        </Stack>
      </Box>

      <Box as="hr" borderTop="1px solid" borderColor="gray.200" width="100%" my={2} />

      <Box width="100%">
        <Heading size="md" mb={4}>Noto Sans TC (Traditional Chinese)</Heading>
        
        <Stack direction="column" gap={3}>
          <ChineseText fontWeight="light">
            輕量 (Light) - 這段文字使用了 Noto Sans TC 輕量字重。
          </ChineseText>
          
          <ChineseText fontWeight="normal">
            標準 (Normal) - 這段文字使用了 Noto Sans TC 標準字重。
          </ChineseText>
          
          <ChineseText fontWeight="medium">
            中等 (Medium) - 這段文字使用了 Noto Sans TC 中等字重。
          </ChineseText>
          
          <ChineseText fontWeight="bold">
            粗體 (Bold) - 這段文字使用了 Noto Sans TC 粗體字重。
          </ChineseText>
          
          <ChineseText fontWeight="black">
            黑體 (Black) - 這段文字使用了 Noto Sans TC 黑體字重。
          </ChineseText>
        </Stack>
      </Box>

      <Box as="hr" borderTop="1px solid" borderColor="gray.200" width="100%" my={2} />

      <Box width="100%">
        <Heading size="md" mb={4}>Custom Components with Font Weights</Heading>
        
        <Stack direction="row" gap={4} flexWrap="wrap">
          <ChineseButton colorScheme="blue">
            標準按鈕
          </ChineseButton>
          
          <ChineseButton colorScheme="blue" fontWeight="bold">
            粗體按鈕
          </ChineseButton>
          
          <BoldChineseButton colorScheme="purple">
            預設粗體按鈕
          </BoldChineseButton>
        </Stack>
      </Box>
      
      <Box as="hr" borderTop="1px solid" borderColor="gray.200" width="100%" my={2} />
      
      <Box width="100%">
        <Heading size="md" mb={4}>Using the withFont HOC</Heading>
        <Text mb={2}>
          To create component with custom font and weight:
        </Text>
        <Box 
          bg="gray.100" 
          color="gray.800" 
          p={4} 
          borderRadius="md" 
          fontFamily="monospace"
          fontSize="sm"
        >
          {`// Create component with default normal weight
const ChineseButton = withFont(Button, "var(--font-sans-tc)");

// Create component with default bold weight
const BoldChineseButton = withFont(Button, "var(--font-sans-tc)", "bold");

// Create component with specific numeric weight
const SemiBoldText = withFont(Text, "var(--font-serif-literary)", 600);

// Usage:
<ChineseButton>Normal Weight</ChineseButton>
<ChineseButton fontWeight="bold">Override to Bold</ChineseButton>`}
        </Box>
      </Box>
    </Stack>
  );
};
