import { Image, Box, Stack, Flex, Button } from "@chakra-ui/react"
import useEmblaCarousel from 'embla-carousel-react'
import { useCallback, useEffect, useState } from 'react'
import { colors } from '../../colors'
const imageBaseUrl = import.meta.env.VITE_IMAGE_URL;
function ImagesSilder({ images }: { images: string[] }) {

    // Initialize Embla Carousel
    const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true })
    const [selectedIndex, setSelectedIndex] = useState(0)


    // Update the selected index when the slide changes
    const onSelect = useCallback(() => {
        if (!emblaApi) return
        setSelectedIndex(emblaApi.selectedScrollSnap())
    }, [emblaApi])

    // Scroll to slide when a dot is clicked
    const scrollTo = useCallback(
        (index: number) => emblaApi && emblaApi.scrollTo(index),
        [emblaApi]
    )

    useEffect(() => {
        if (!emblaApi) return
        onSelect()
        emblaApi.on('select', onSelect)
        return () => {
            emblaApi.off('select', onSelect)
        }
    }, [emblaApi, onSelect])


    return (

        <Stack
            w='full'
            align={'center'}
        >
            <Box className="embla" overflow="hidden" ref={emblaRef} w='90vw' mt={5}>
                <Box className="embla__container" display="flex">
                    {images.map((item) => (
                        <Box
                            className="embla__slide"
                            flex="0 0 100%"
                            minWidth="0"
                        >
                            <Image
                                src={`${imageBaseUrl}${item}`}
                                w="100%"
                                h="auto"
                            />
                        </Box>
                    ))}
                </Box>
            </Box>

            {/* Dots */}
            <Flex justify="center" align={'center'} mt={4}>
                {images.map((_, index) => (
                    <Flex
                        key={index}
                        onClick={() => scrollTo(index)}
                        minWidth="5px"
                        minHeight="5px"
                        width={index === selectedIndex ? "10px" : "5px"}
                        height={index === selectedIndex ? "10px" : "5px"}
                        padding="0"
                        borderRadius="50%"
                        mx={1}
                        bg={colors.mainColor}

                    />
                ))}
            </Flex>

        </Stack>
    )

}

export default ImagesSilder