import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"
import { useState, useEffect } from "react"

import {
  type Body_login_login_access_token as AccessToken,
  type ApiError,
  ClientsService,
  type ClientPublic,
  type ClientRegister,
  //UsersService,
} from "@/client"
import { handleError } from "../utils"

// Define a query key for auth state that components can subscribe to
export const AUTH_QUERY_KEY = ['authState']

export interface AuthState {
  isAuthenticated: boolean;
}

const isLoggedIn = () => {
  return localStorage.getItem("access_token") !== null
}

const useAuth = () => {
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const [error, setError] = useState<string | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(isLoggedIn())
  const [authUpdate, triggerAuthUpdate] = useState(false)

  // Listen for storage events (for multi-tab support)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'access_token') {
        const newAuthState = isLoggedIn()
        setIsAuthenticated(newAuthState)
        triggerAuthUpdate(prev => !prev)
        
        // Update React Query cache
        queryClient.setQueryData<AuthState>(AUTH_QUERY_KEY, { 
          isAuthenticated: newAuthState 
        })
      }
    }

    window.addEventListener('storage', handleStorageChange)
    
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [queryClient])

  // Synchronize auth state with React Query cache
  useEffect(() => {
    const currentAuthState = isLoggedIn()
    setIsAuthenticated(currentAuthState)
    // Update React Query cache to inform other components
    queryClient.setQueryData<AuthState>(AUTH_QUERY_KEY, {
      isAuthenticated: currentAuthState
    })
    console.log('Auth state updated:', currentAuthState) // Debug log
  }, [authUpdate, queryClient])

  // const { data: user } = useQuery<UserPublic | null, Error>({
  //   queryKey: ["currentUser"],
  //   queryFn: UsersService.readUserMe,
  //   enabled: isLoggedIn(),
  // })

  const signUpMutation = useMutation({
    mutationFn: (data: ClientRegister) =>
      ClientsService.registerClient({ requestBody: data }),

    onSuccess: () => {
      navigate({ to: "/verify-email" })
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] })
    },
  })

  const login = async (data: AccessToken) => {
    const response = await ClientsService.clientLoginAccessToken({
      formData: data,
    })
    localStorage.setItem("access_token", response.access_token)
    setIsAuthenticated(true)
    triggerAuthUpdate(prev => !prev)

    // Update React Query cache instead of dispatching event
    queryClient.setQueryData<AuthState>(AUTH_QUERY_KEY, { isAuthenticated: true })

    // Dispatch custom event for components to listen to
    window.dispatchEvent(new CustomEvent('authStateChanged', { detail: { isAuthenticated: true } }))

    console.log('Login successful, auth state set to true') // Debug log

    return response
  }

  const loginMutation = useMutation({
    mutationFn: login,
    onSuccess: () => {
      // Ensure authentication state is updated before navigation
      setIsAuthenticated(true)
      
      // Update React Query cache instead of dispatching event
      queryClient.setQueryData<AuthState>(AUTH_QUERY_KEY, { isAuthenticated: true })
      queryClient.invalidateQueries()
      
      navigate({ to: "/" })
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
  })

  const logout = () => {
    localStorage.removeItem("access_token")
    setIsAuthenticated(false)
    triggerAuthUpdate(prev => !prev)

    // Update React Query cache instead of dispatching event
    queryClient.setQueryData<AuthState>(AUTH_QUERY_KEY, { isAuthenticated: false })

    // Clear specific cached data but keep auth state
    queryClient.removeQueries({ queryKey: ["currentClient"] })
    queryClient.removeQueries({ queryKey: ["clients"] })

    // Dispatch custom event for components to listen to
    window.dispatchEvent(new CustomEvent('authStateChanged', { detail: { isAuthenticated: false } }))

    console.log('Logout successful, auth state set to false') // Debug log

    navigate({ to: "/signin" })
  }

  return {
    signUpMutation,
    loginMutation,
    logout,
    //user,
    error,
    isAuthenticated,
    resetError: () => setError(null),
  }
}

export { isLoggedIn }
export default useAuth
