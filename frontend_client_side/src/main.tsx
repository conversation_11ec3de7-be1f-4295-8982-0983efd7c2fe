import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import { MutationCache, QueryCache, QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { Provider } from "@/components/ui/provider"
import { routeTree } from "./routeTree.gen";
import { RouterProvider, createRouter } from "@tanstack/react-router";
import { ApiError, OpenAPI } from "./client"

OpenAPI.BASE = 'http://localhost:8000'
OpenAPI.TOKEN = async () => {
  return localStorage.getItem("access_token") || ""
}

const handleApiError = (error: Error) => {
  if (error instanceof ApiError && [401, 403].includes(error.status)) {
    localStorage.removeItem("access_token")
    window.location.href = "/login"
  }
}


// Create a new router instance
const router = createRouter({ routeTree });
// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

// const handleApiError = (error: Error) => {
//   if (error instanceof ApiError && [401, 403].includes(error.status)) {
//     localStorage.removeItem("access_token")
//     window.location.href = "/login"
//   }
// }

const queryClient = new QueryClient({
  queryCache: new QueryCache({
    // onError: handleApiError,
  }),
  mutationCache: new MutationCache({
    //onError: handleApiError,
  }),
})


createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider>
      <QueryClientProvider client={queryClient}>
        <RouterProvider router={router} />
      </QueryClientProvider>
    </Provider>
  </StrictMode>,
)
