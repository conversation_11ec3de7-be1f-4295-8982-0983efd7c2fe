"""add_client_questionnaires_table

Revision ID: a76dfd300af4
Revises: 00d9de59e269
Create Date: 2025-07-12 12:26:14.961190

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a76dfd300af4'
down_revision = '00d9de59e269'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('questionnaire',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('birth_info', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('caregiver', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('feeding', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('native_language', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('status', sa.Enum('available', 'finished', name='questionnairestatusenum'), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('client_id', sa.Uuid(), nullable=False),
    sa.Column('survey_type_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('finished_at', sa.DateTime(), nullable=True),
    sa.Column('questions_and_ans', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['client.id'], ),
    sa.ForeignKeyConstraint(['survey_type_id'], ['surveytype.survey_type_id'], ),
    sa.PrimaryKeyConstraint('id')
    )

    # Create indexes for better performance
    op.create_index('ix_questionnaire_client_id', 'questionnaire', ['client_id'])
    op.create_index('ix_questionnaire_status', 'questionnaire', ['status'])
    op.create_index('ix_questionnaire_created_at', 'questionnaire', ['created_at'])

    # Add unique constraint to ensure only one 'available' questionnaire per client
    # This uses a partial unique index which allows multiple 'finished' questionnaires
    # but only one 'available' questionnaire per client
    op.execute("""
        CREATE UNIQUE INDEX ix_questionnaire_unique_available_per_client
        ON questionnaire (client_id)
        WHERE status = 'available'
    """)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop indexes first
    op.drop_index('ix_questionnaire_unique_available_per_client', 'questionnaire')
    op.drop_index('ix_questionnaire_created_at', 'questionnaire')
    op.drop_index('ix_questionnaire_status', 'questionnaire')
    op.drop_index('ix_questionnaire_client_id', 'questionnaire')

    # Drop the table
    op.drop_table('questionnaire')
    # ### end Alembic commands ###
