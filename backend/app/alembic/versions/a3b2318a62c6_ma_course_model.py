"""ma course model

Revision ID: a3b2318a62c6
Revises: 5d3de6d96d47
Create Date: 2025-03-29 13:25:19.715778

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a3b2318a62c6'
down_revision = '5d3de6d96d47'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('macourseimage',
    sa.Column('image', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.<PERSON>umn('index', sa.Integer(), nullable=False),
    sa.<PERSON>umn('status', sa.<PERSON>(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('macourseimage')
    # ### end Alembic commands ###
