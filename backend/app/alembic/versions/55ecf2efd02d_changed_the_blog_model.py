"""changed the blog model

Revision ID: 55ecf2efd02d
Revises: 17f4c7109a48
Create Date: 2025-04-01 11:18:27.989046

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '55ecf2efd02d'
down_revision = '17f4c7109a48'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('blog',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=11024), nullable=True),
    sa.Column('index', sa.Integer(), nullable=False),
    sa.Column('image1', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('image2', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('image3', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('status', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('blog')
    # ### end Alembic commands ###
