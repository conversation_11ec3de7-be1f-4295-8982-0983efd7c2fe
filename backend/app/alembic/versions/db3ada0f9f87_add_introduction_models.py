"""add introduction models

Revision ID: db3ada0f9f87
Revises: d98aaee1a409
Create Date: 2025-03-22 08:43:02.852199

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'db3ada0f9f87'
down_revision = 'd98aaee1a409'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('introduction',
    sa.<PERSON>umn('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.<PERSON>umn('description', sqlmodel.sql.sqltypes.AutoString(length=1024), nullable=True),
    sa.<PERSON>umn('index', sa.Integer(), nullable=False),
    sa.Column('image', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('status', sa.<PERSON>(), nullable=False),
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('introduction')
    # ### end Alembic commands ###
