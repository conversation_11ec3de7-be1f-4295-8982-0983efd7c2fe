"""web setting model

Revision ID: 152b6cd4f473
Revises: 4676f97f154c
Create Date: 2025-04-04 05:26:04.732038

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '152b6cd4f473'
down_revision = '4676f97f154c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('setting',
    sa.Column('address', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('latitude', sa.Float(), nullable=False),
    sa.Column('longitude', sa.Float(), nullable=False),
    sa.Column('phone', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.<PERSON>umn('email', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('facebook', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('whatsapp', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('setting')
    # ### end Alembic commands ###
