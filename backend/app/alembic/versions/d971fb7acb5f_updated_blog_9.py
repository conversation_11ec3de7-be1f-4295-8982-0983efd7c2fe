"""updated blog 9

Revision ID: d971fb7acb5f
Revises: a3885d128f77
Create Date: 2025-03-31 13:24:57.966934

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'd971fb7acb5f'
down_revision = 'a3885d128f77'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('blogimage', sa.Column('blog_id', sa.Uuid(), nullable=False))
    op.drop_constraint('blogimage_blogId_fkey', 'blogimage', type_='foreignkey')
    op.create_foreign_key(None, 'blogimage', 'blog', ['blog_id'], ['id'], ondelete='CASCADE')
    op.drop_column('blogimage', 'blogId')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('blogimage', sa.Column('blogId', sa.UUID(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'blogimage', type_='foreignkey')
    op.create_foreign_key('blogimage_blogId_fkey', 'blogimage', 'blog', ['blogId'], ['id'], ondelete='CASCADE')
    op.drop_column('blogimage', 'blog_id')
    # ### end Alembic commands ###
