"""blog deleted index

Revision ID: 792ba846299c
Revises: 55ecf2efd02d
Create Date: 2025-04-01 11:23:50.613849

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '792ba846299c'
down_revision = '55ecf2efd02d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('blog', 'index')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('blog', sa.Column('index', sa.INTEGER(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
