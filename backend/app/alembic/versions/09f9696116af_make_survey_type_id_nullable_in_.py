"""Make survey_type_id nullable in questionnaire

Revision ID: 09f9696116af
Revises: a76dfd300af4
Create Date: 2025-07-12 12:58:45.715302

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '09f9696116af'
down_revision = 'a76dfd300af4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('questionnaire', 'survey_type_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_index('ix_questionnaire_client_id', table_name='questionnaire')
    op.drop_index('ix_questionnaire_created_at', table_name='questionnaire')
    op.drop_index('ix_questionnaire_status', table_name='questionnaire')
    op.drop_index('ix_questionnaire_unique_available_per_client', table_name='questionnaire', postgresql_where="(status = 'available'::questionnairestatusenum)")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_questionnaire_unique_available_per_client', 'questionnaire', ['client_id'], unique=True, postgresql_where="(status = 'available'::questionnairestatusenum)")
    op.create_index('ix_questionnaire_status', 'questionnaire', ['status'], unique=False)
    op.create_index('ix_questionnaire_created_at', 'questionnaire', ['created_at'], unique=False)
    op.create_index('ix_questionnaire_client_id', 'questionnaire', ['client_id'], unique=False)
    op.alter_column('questionnaire', 'survey_type_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
