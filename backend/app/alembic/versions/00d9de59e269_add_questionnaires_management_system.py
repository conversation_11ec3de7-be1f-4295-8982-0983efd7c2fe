"""add questionnaires management system

Revision ID: 00d9de59e269
Revises: 2025070101
Create Date: 2025-07-05 07:07:25.048043

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '00d9de59e269'
down_revision = '2025070101'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('intelligencecategory',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('category_id')
    )
    op.create_table('surveytype',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('survey_type_id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('survey_type_id')
    )
    op.create_table('corecompetency',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('competency_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['intelligencecategory.category_id'], ),
    sa.PrimaryKeyConstraint('competency_id')
    )
    op.create_table('question',
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('index', sa.Integer(), nullable=False),
    sa.Column('competency_id', sa.Integer(), nullable=False),
    sa.Column('survey_type_id', sa.Integer(), nullable=False),
    sa.Column('question_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['competency_id'], ['corecompetency.competency_id'], ),
    sa.ForeignKeyConstraint(['survey_type_id'], ['surveytype.survey_type_id'], ),
    sa.PrimaryKeyConstraint('question_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('question')
    op.drop_table('corecompetency')
    op.drop_table('surveytype')
    op.drop_table('intelligencecategory')
    # ### end Alembic commands ###
