"""add_created_at_to_client_safe

Revision ID: 2025070101
Revises: 152b6cd4f473
Create Date: 2025-07-01 11:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision = '2025070101'
down_revision = '152b6cd4f473'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if the column already exists before adding it
    connection = op.get_bind()
    result = connection.execute(text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='client' AND column_name='created_at'
    """))
    
    if not result.fetchone():
        # Column doesn't exist, add it
        op.add_column('client', sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')))
    else:
        # Column already exists, just ensure it has the right default
        op.alter_column('client', 'created_at', server_default=sa.text('CURRENT_TIMESTAMP'))


def downgrade() -> None:
    # Check if the column exists before trying to drop it
    connection = op.get_bind()
    result = connection.execute(text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='client' AND column_name='created_at'
    """))
    
    if result.fetchone():
        op.drop_column('client', 'created_at')
