"""add blog model

Revision ID: 336c19d22342
Revises: d65ebe515285
Create Date: 2025-03-31 09:13:14.813005

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '336c19d22342'
down_revision = 'd65ebe515285'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('blog',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=11024), nullable=True),
    sa.Column('index', sa.Integer(), nullable=False),
    sa.Column('status', sa.<PERSON>an(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Primary<PERSON>ey<PERSON>onstraint('id')
    )
    op.create_table('blogimage',
    sa.Column('image', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('index', sa.Integer(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('blogId', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['blogId'], ['blog.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('blogimage')
    op.drop_table('blog')
    # ### end Alembic commands ###
