"""Add Blog model

Revision ID: d65ebe515285
Revises: a3b2318a62c6
Create Date: 2025-03-31 09:08:04.502910

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'd65ebe515285'
down_revision = 'a3b2318a62c6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
