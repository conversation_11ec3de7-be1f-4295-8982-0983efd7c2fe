"""Seed default survey types

Revision ID: seed_survey_types
Revises: 09f9696116af
Create Date: 2025-01-17 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'seed_survey_types'
down_revision = '09f9696116af'
branch_labels = None
depends_on = None


def upgrade():
    # Insert default survey types
    survey_types_table = sa.table(
        'surveytype',
        sa.column('survey_type_id', sa.Integer),
        sa.column('name', sa.String)
    )
    
    op.bulk_insert(survey_types_table, [
        {'survey_type_id': 1, 'name': '3-6 years questionnaire'},
        {'survey_type_id': 2, 'name': '7-11 years questionnaire'},
        {'survey_type_id': 3, 'name': '12-15 years questionnaire'},
    ])


def downgrade():
    # Remove the seeded survey types
    op.execute("DELETE FROM surveytype WHERE survey_type_id IN (1, 2, 3)")
