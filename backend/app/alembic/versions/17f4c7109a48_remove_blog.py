""" remove blog

Revision ID: 17f4c7109a48
Revises: d971fb7acb5f
Create Date: 2025-03-31 13:27:18.910850

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '17f4c7109a48'
down_revision = 'd971fb7acb5f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('blogimage')
    op.drop_table('blog')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('blog',
    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=11024), autoincrement=False, nullable=True),
    sa.Column('index', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('status', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='blog_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('blogimage',
    sa.Column('image', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('index', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('blog_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['blog_id'], ['blog.id'], name='blogimage_blog_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='blogimage_pkey')
    )
    # ### end Alembic commands ###
