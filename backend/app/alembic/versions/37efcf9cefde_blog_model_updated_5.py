"""blog model updated 5 

Revision ID: 37efcf9cefde
Revises: 136fd1d87311
Create Date: 2025-03-31 13:20:37.694833

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '37efcf9cefde'
down_revision = '136fd1d87311'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
