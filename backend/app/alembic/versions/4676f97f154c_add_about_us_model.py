"""Add about us model

Revision ID: 4676f97f154c
Revises: 792ba846299c
Create Date: 2025-04-03 07:32:20.309230

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '4676f97f154c'
down_revision = '792ba846299c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('aboutus',
    sa.<PERSON>umn('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.<PERSON>umn('description', sqlmodel.sql.sqltypes.AutoString(length=1024), nullable=True),
    sa.<PERSON>umn('index', sa.Integer(), nullable=False),
    sa.Column('image', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('status', sa.<PERSON>(), nullable=False),
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('aboutus')
    # ### end Alembic commands ###
