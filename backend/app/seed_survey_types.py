#!/usr/bin/env python3
"""
Script to seed default survey types into the database.
Run this script after database migrations to populate default survey types.

Usage:
    python app/seed_survey_types.py
"""

import logging
from sqlmodel import Session, select
from app.core.db import engine
from app.models import SurveyType

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def seed_survey_types():
    """Seed default survey types into the database."""
    with Session(engine) as session:
        # Check if survey types already exist
        existing_survey_types = session.exec(select(SurveyType)).all()
        
        if existing_survey_types:
            logger.info(f"Survey types already exist ({len(existing_survey_types)} found). Skipping seeding.")
            return
        
        # Define default survey types
        default_survey_types = [
            {"survey_type_id": 1, "name": "3-6 years questionnaire"},
            {"survey_type_id": 2, "name": "7-11 years questionnaire"},
            {"survey_type_id": 3, "name": "12-15 years questionnaire"},
        ]
        
        # Create and add survey types
        for survey_data in default_survey_types:
            survey_type = SurveyType(
                survey_type_id=survey_data["survey_type_id"],
                name=survey_data["name"]
            )
            session.add(survey_type)
            logger.info(f"Added survey type: {survey_data['name']}")
        
        # Commit changes
        session.commit()
        logger.info(f"Successfully seeded {len(default_survey_types)} survey types.")


def main():
    """Main function to run the seeding process."""
    logger.info("Starting survey types seeding...")
    try:
        seed_survey_types()
        logger.info("Survey types seeding completed successfully.")
    except Exception as e:
        logger.error(f"Error seeding survey types: {e}")
        raise


if __name__ == "__main__":
    main()
