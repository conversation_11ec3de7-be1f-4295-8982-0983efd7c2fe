import logging
import os
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from pathlib import Path
from uuid import uuid4
from botocore.exceptions import Client<PERSON>rror
from typing import Any
import boto3
from PIL import Image
import emails  # type: ignore
import jwt
from jinja2 import Template
from jwt.exceptions import InvalidTokenError
from typing import IO
import filetype
from app.core import security
from app.core.config import settings
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status

static = "static"

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

ConnectionUrl = f"https://{settings.AccountID}.r2.cloudflarestorage.com"

r2 = boto3.client(
    "s3",
    endpoint_url=ConnectionUrl,
    aws_access_key_id=settings.access_key_id,
    aws_secret_access_key=settings.secret_access_key,
)


@dataclass
class EmailData:
    html_content: str
    subject: str


def render_email_template(*, template_name: str, context: dict[str, Any]) -> str:
    template_str = (
        Path(__file__).parent / "email-templates" / "build" / template_name
    ).read_text()
    html_content = Template(template_str).render(context)
    return html_content


def send_email(
    *,
    email_to: str,
    subject: str = "",
    html_content: str = "",
) -> None:
    assert settings.emails_enabled, "no provided configuration for email variables"
    message = emails.Message(
        subject=subject,
        html=html_content,
        mail_from=(settings.EMAILS_FROM_NAME, settings.EMAILS_FROM_EMAIL),
    )
    smtp_options = {"host": settings.SMTP_HOST, "port": settings.SMTP_PORT}
    if settings.SMTP_TLS:
        smtp_options["tls"] = True
    elif settings.SMTP_SSL:
        smtp_options["ssl"] = True
    if settings.SMTP_USER:
        smtp_options["user"] = settings.SMTP_USER
    if settings.SMTP_PASSWORD:
        smtp_options["password"] = settings.SMTP_PASSWORD
    logger.info(smtp_options)
    response = message.send(to=email_to, smtp=smtp_options)
    logger.info(f"send email result: {response}")


def generate_test_email(email_to: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Test email"
    html_content = render_email_template(
        template_name="test_email.html",
        context={"project_name": settings.PROJECT_NAME, "email": email_to},
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_test_email(email_to: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Test email"
    html_content = render_email_template(
        template_name="test_email.html",
        context={"project_name": settings.PROJECT_NAME, "email": email_to},
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_reset_password_email(email_to: str, email: str, token: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Password recovery for user {email}"
    link = f"{settings.FRONTEND_HOST}/reset-password?token={token}"
    html_content = render_email_template(
        template_name="reset_password.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": email,
            "email": email_to,
            "valid_hours": settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS,
            "link": link,
        },
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_new_account_email(
    email_to: str, username: str, password: str
) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - New account for user {username}"
    html_content = render_email_template(
        template_name="new_account.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": username,
            "password": password,
            "email": email_to,
            "link": settings.FRONTEND_HOST,
        },
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_password_reset_token(email: str) -> str:
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.now(timezone.utc)
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email},
        settings.SECRET_KEY,
        algorithm=security.ALGORITHM,
    )
    return encoded_jwt


def generate_email_verification(email_to: str, email: str, token: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Verify your email"
    link = f"{settings.CLIENT_HOST}/verify-email?token={token}"
    html_content = render_email_template(
        template_name="verify_email.html",  # You'll need to create this template
        context={
            "project_name": settings.PROJECT_NAME,
            "username": email,
            "email": email_to,
            "valid_days": settings.EMAIL_VERIFICATION_TOKEN_EXPIRE_DAYS,
            "link": link,
        },
    )
    return EmailData(html_content=html_content, subject=subject)


def verify_password_reset_token(token: str) -> str | None:
    try:
        decoded_token = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[security.ALGORITHM]
        )
        return str(decoded_token["sub"])
    except InvalidTokenError:
        return None


def generate_email_verification_token(email: str) -> str:
    delta = timedelta(days=settings.EMAIL_VERIFICATION_TOKEN_EXPIRE_DAYS)
    now = datetime.now(timezone.utc)
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email, "type": "verification"},
        settings.SECRET_KEY,
        algorithm=security.ALGORITHM,
    )
    return encoded_jwt


def verify_email_verification_token(token: str) -> str | None:
    try:
        decoded_token = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[security.ALGORITHM]
        )
        if decoded_token.get("type") != "verification":
            return None
        return str(decoded_token["sub"])
    except InvalidTokenError:
        return None


def validate_file_size_type(file: IO):
    FILE_SIZE = 5097152  # 2MB

    accepted_file_types = [
        "image/png",
        "image/jpeg",
        "image/jpg",
        "image/heic",
        "image/heif",
        "image/heics",
        "png",
        "jpeg",
        "jpg",
        "heic",
        "heif",
        "heics",
    ]
    file_info = filetype.guess(file.file)
    if file_info is None:
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail="Unable to determine file type",
        )

    detected_content_type = file_info.extension.lower()

    if (
        file.content_type not in accepted_file_types
        or detected_content_type not in accepted_file_types
    ):
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail="Unsupported file type",
        )

    real_file_size = 0
    for chunk in file.file:
        real_file_size += len(chunk)
        if real_file_size > FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE, detail="Too large"
            )


def upload_file(file_name, bucket_name, object_name=None):

    if object_name is None:
        object_name = file_name

    try:
        r2.upload_file(file_name, bucket_name, object_name)
    except ClientError as e:
        print(f"An error occurred: {e}")
        return False
    return True


def delete_file(file_name, bucket_name, object_name=None):
    if object_name is None:
        object_name = file_name
    try:
        r2.delete_object(Bucket=bucket_name, Key=file_name)

    except ClientError as e:
        print(f"An error occurred: {e}")
        return False
    return True


async def convert_png_to_webp(file_path: str) -> str:
    """
    Checks if the image at file_path is a PNG and converts it to WebP format if it is.
    Returns the path to the new WebP file or the original file path if not a PNG.
    Does NOT handle R2 uploads - that should be done by the calling function.
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return file_path

        # Check if it's a PNG file
        img = Image.open(file_path)
        if img.format == "PNG":
            # Create new file path with .webp extension
            file_name, _ = os.path.splitext(file_path)
            webp_path = f"{file_name}.webp"

            # Convert and save as WebP with good quality and lossless for PNGs with transparency
            if img.mode in ("RGBA", "LA") or (
                img.mode == "P" and "transparency" in img.info
            ):
                # Image has transparency, use lossless
                img.save(webp_path, format="WEBP", lossless=True)
            else:
                # No transparency, use lossy with good quality
                img.save(webp_path, format="WEBP", quality=85)

            logger.info(f"Converted PNG to WebP: {file_path} -> {webp_path}")

            # Delete the original PNG file
            os.remove(file_path)

            return webp_path

        return file_path
    except Exception as e:
        logger.error(f"Error converting PNG to WebP: {e}")
        return file_path


async def save_picture(file, folder_name: str = "", file_name: str = None):

    randon_uid = str(uuid4())
    _, f_ext = os.path.splitext(file.filename)

    picture_name = (
        randon_uid if file_name is None else file_name.lower().replace(" ", "")
    ) + f_ext

    path = os.path.join(static, folder_name)
    if not os.path.exists(path):
        os.makedirs(path)

    picture_path = os.path.join(path, picture_name)

    img = Image.open(file.file)
    img.save(picture_path)
    # Convert PNG to WebP if applicable
    picture_path = await convert_png_to_webp(picture_path)

    if upload_file(picture_path, "images"):
        print(f"File {picture_path} uploaded successfully to images")
    else:
        print(f"File upload failed")

    return picture_path


async def del_picture(picture_path):
    try:
        os.remove(picture_path)
        delete_file(picture_path, "images")
    except Exception as e:
        print("Error: ", e)
        return False
    return True
