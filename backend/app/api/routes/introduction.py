
from typing import Any, Annotated
from app.utils import validate_file_size_type, save_picture, del_picture
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from sqlmodel import func, select

from app.api.deps import CurrentUser, SessionDep
from app.models import (
    IntroductionCreate,
    IntroductionsPublic,
    Introduction,
    IntroductionUpdate,
    Message,
)

router = APIRouter(tags=["introduction"], prefix="/introduction")


@router.post(
    "/docs",
    response_model=Introduction,
    include_in_schema=True,
)
def create_introduction_docs(introduction: IntroductionCreate) -> Any:
    """
    API Schema for documentation
    """
    pass



@router.post(
    "/",
    response_model=Introduction,
)
async def create_introduction(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    description: str = Form(),
    title: str = Form(),
    image: Annotated[UploadFile, File()],
    index: int = Form()
) -> Any:
    """
    Create new about us.
    """
    validate_file_size_type(image)
    imageUrl = await save_picture(file=image, folder_name="tmp")
    introduction_in = IntroductionCreate(
        description=description, image=imageUrl, index=index, title=title
    )
    introduction = Introduction.model_validate(introduction_in)
    session.add(introduction)
    session.commit()
    session.refresh(introduction)
    return introduction


@router.put("/{id}", response_model=Introduction)
async def edit_introduction(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    id: int,
    description: str = Form(),
    image: Annotated[UploadFile, File()] = None,
    title: str = Form(),
    index: int = Form()
) -> Any:
    introduction = session.get(Introduction, id)

    if image is not None:
        validate_file_size_type(image)
        imageUrl = await save_picture(file=image, folder_name="tmp")
        await del_picture(introduction.image)
        introduction_in = IntroductionUpdate(
            description=description, image=imageUrl, index=index, title=title
        )
    else:
        introduction_in = IntroductionUpdate(
            description=description, image=introduction.image, index=index, title=title
        )

    update_dict = introduction_in.model_dump(exclude_unset=True)
    introduction.sqlmodel_update(update_dict)
    session.add(introduction)
    session.commit()
    session.refresh(introduction)

    return introduction


@router.get("/", response_model=IntroductionsPublic)
def read_introduction_list(session: SessionDep, skip: int = 0, limit: int = 100) -> Any:

    count_statement = select(func.count()).select_from(Introduction)
    count = session.exec(count_statement).one()
    statement = select(Introduction).offset(skip).limit(limit)
    introduction = session.exec(statement).all()

    return IntroductionsPublic(data=introduction, count=count)


@router.delete("/{id}")
async def delete_introduction(
    session: SessionDep, current_user: CurrentUser, id: int
) -> Message:
    """
    Delete an course.
    """
    introduction = session.get(Introduction, id)
    if not introduction:
        raise HTTPException(status_code=404, detail="introduction not found")
    await del_picture(introduction.image)
    session.delete(introduction)
    session.commit()
    return Message(message="introduction deleted successfully")
