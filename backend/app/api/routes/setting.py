import uuid
from typing import Any

from fastapi import APIRouter, HTTPException
from sqlmodel import func, select
from typing import List
from app.api.deps import CurrentUser, SessionDep
from app.models import SettingBase, Setting

router = APIRouter(tags=["setting"], prefix="/setting")


@router.get(
    "/",
    response_model=Setting,
)
def read_setting(session: SessionDep) -> Any:
    """
    Retrieve users.
    """
    setting = session.exec(select(Setting)).first()
    if not setting:
        raise HTTPException(status_code=404, detail="Setting not found")
    return setting


@router.put("/", response_model=SettingBase)
def update_setting(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    setting_in: SettingBase,
) -> Any:
    """
    Update an item.
    """
    setting = session.exec(select(Setting)).first()
    update_dict = setting_in.model_dump(exclude_unset=True)
    setting.sqlmodel_update(update_dict)
    session.add(setting)
    session.commit()
    session.refresh(setting)
    return setting
