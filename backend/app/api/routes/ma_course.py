from typing import Any, Annotated
from app.utils import validate_file_size_type, save_picture, del_picture
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from sqlmodel import func, select
import uuid
from app.api.deps import CurrentUser, SessionDep
from app.models import (
    MaCourseImageCreate,
    MaCourseImagesPublic,
    MaCourseImage,
    MaCourseImageUpdate,
)


router = APIRouter(tags=["ma_course"], prefix="/ma_course")


@router.post(
    "/",
    response_model=MaCourseImage,
)
async def create_macourse(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    image: Annotated[UploadFile, File()],
    index: int = Form(),
) -> Any:

    validate_file_size_type(image)
    imageUrl = await save_picture(file=image, folder_name="tmp")
    maCourse_in = MaCourseImageCreate(image=imageUrl, index=index)
    maCourse = MaCourseImage.model_validate(maCourse_in)
    session.add(maCourse)
    session.commit()
    session.refresh(maCourse)
    return maCourse


@router.put("/{id}", response_model=MaCourseImage)
async def edit_macourse(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    id: uuid.UUID,
    index: int = Form(),
) -> Any:
    """
    Edit an existing MaCourse.
    """
    macourse = session.get(MaCourseImage, id)

    if not macourse:
        raise HTTPException(status_code=404, detail="Ma Course not found")
    macourse_in = MaCourseImageUpdate(
        index=index, image=macourse.image, status=macourse.status
    )

    update_dict = macourse_in.model_dump(exclude_unset=True)
    macourse.sqlmodel_update(update_dict)
    session.add(macourse)
    session.commit()
    session.refresh(macourse)
    return macourse


@router.get("/", response_model=MaCourseImagesPublic)
async def get_macourses(session: SessionDep, skip: int = 0, limit: int = 100) -> Any:
    count_statement = select(func.count()).select_from(MaCourseImage)
    count = session.exec(count_statement).one()
    statement = select(MaCourseImage).offset(skip).limit(limit)
    maCourseImage = session.exec(statement).all()

    return MaCourseImagesPublic(data=maCourseImage, count=count)


@router.delete("/{id}")
async def delete_macourse(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    id: uuid.UUID,
) -> Any:
    """
    Delete a MaCourse.
    """
    macourse = session.get(MaCourseImage, id)

    if not macourse:
        raise HTTPException(status_code=404, detail="Ma Course not found")

    await del_picture(macourse.image)

    session.delete(macourse)
    session.commit()

    return {"message": "Ma Course image deleted successfully"}
