from typing import Any, Annotated
from app.utils import validate_file_size_type, save_picture, del_picture
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from sqlmodel import func, select

from app.api.deps import CurrentUser, SessionDep
from app.models import (
    AboutUsCreate,
    AboutUsPublic,
    AboutUs,
    AboutUsUpdate,
    Message,
)

router = APIRouter(tags=["aboutus"], prefix="/aboutus")


@router.post(
    "/",
    response_model=AboutUs,
)
async def create_about_us(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    description: str = Form(),
    title: str = Form(),
    image: Annotated[UploadFile, File()],
    index: int = Form()
) -> Any:
    """
    Create new about us.
    """
    validate_file_size_type(image)
    imageUrl = await save_picture(file=image, folder_name="tmp")
    aboutUs_in = AboutUsCreate(
        description=description, image=imageUrl, index=index, title=title
    )
    aboutUs = AboutUs.model_validate(aboutUs_in)
    session.add(aboutUs)
    session.commit()
    session.refresh(aboutUs)
    return aboutUs


@router.put("/{id}", response_model=AboutUs)
async def edit_about_us(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    id: int,
    description: str = Form(),
    image: Annotated[UploadFile, File()] = None,
    title: str = Form(),
    index: int = Form()
) -> Any:
    aboutUs = session.get(AboutUs, id)

    if image is not None:
        validate_file_size_type(image)
        imageUrl = await save_picture(file=image, folder_name="tmp")
        await del_picture(aboutUs.image)
        aboutUs_in = AboutUsUpdate(
            description=description, image=imageUrl, index=index, title=title
        )
    else:
        aboutUs_in = AboutUsUpdate(
            description=description, image=aboutUs.image, index=index, title=title
        )

    update_dict = aboutUs_in.model_dump(exclude_unset=True)
    aboutUs.sqlmodel_update(update_dict)
    session.add(aboutUs)
    session.commit()
    session.refresh(aboutUs)

    return aboutUs


@router.get("/", response_model=AboutUsPublic)
def read_about_us_list(session: SessionDep, skip: int = 0, limit: int = 100) -> Any:

    count_statement = select(func.count()).select_from(AboutUs)
    count = session.exec(count_statement).one()
    statement = select(AboutUs).offset(skip).limit(limit)
    aboutUs = session.exec(statement).all()

    return AboutUsPublic(data=aboutUs, count=count)


@router.delete("/{id}")
async def delete_about_us(
    session: SessionDep, current_user: CurrentUser, id: int
) -> Message:
 
    aboutUs = session.get(AboutUs, id)
    if not aboutUs:
        raise HTTPException(status_code=404, detail="about us not found")
    await del_picture(aboutUs.image)
    session.delete(aboutUs)
    session.commit()
    return Message(message="about us deleted successfully")
