from datetime import timedel<PERSON>
from typing import Annotated, Any

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.security import OAuth2<PERSON><PERSON>wordRequestForm
from fastapi import BackgroundTasks
from app import crud
from app.api.deps import <PERSON><PERSON><PERSON>, SessionDep, get_current_active_superuser
from app.core import security
from app.core.config import settings
from app.core.security import get_password_hash
from app.models import Message, NewPassword, Token, UserPublic
from app.utils import (
    generate_password_reset_token,
    generate_reset_password_email,
    send_email,
    verify_password_reset_token,
)

router = APIRouter(tags=["client login"])

@router.post("/client_login"/"access-token")
def client_login_access_token(
    session: SessionDep, form_data: Annotated[OAuth2PasswordRequestForm, Depends()]
) -> Token:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    client = crud.client_authenticate(
        session=session, email=form_data.username, password=form_data.password
    )
    if not client:
        raise HTTPException(status_code=400, detail="Incorrect email or password")
    elif not client.is_active:
        raise HTTPException(status_code=400, detail="Inactive client")
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return Token(
        access_token=security.create_access_token(
            client.id, expires_delta=access_token_expires
        )
    )
    
    