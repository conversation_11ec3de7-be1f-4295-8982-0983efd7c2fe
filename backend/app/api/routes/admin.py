import uuid
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import col, func, select

from app import crud
from app.api.deps import (
    CurrentUser,
    SessionDep,
    get_current_active_superuser,
)
from app.core.security import get_password_hash
from app.models import (
    Client,
    ClientCreate,
    ClientPublic,
    ClientsPublic,
    ClientUpdate,
    Message,
)

router = APIRouter(tags=["admin"])


@router.get(
    "/admin_clients",
    dependencies=[Depends(get_current_active_superuser)],
    response_model=ClientsPublic,
)
def read_clients(
    session: SessionDep,
    skip: int = 0,
    limit: int = 100,
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: str = Query("desc", description="Sort order: asc or desc"),
    status_filter: str | None = Query(None, description="Filter by status: active, inactive, or all"),
) -> Any:
    """
    Retrieve clients with pagination, sorting, and filtering.
    """
    # Build base query
    statement = select(Client)
    count_statement = select(func.count()).select_from(Client)
    
    # Apply status filter
    if status_filter == "active":
        statement = statement.where(Client.is_active == True)
        count_statement = count_statement.where(Client.is_active == True)
    elif status_filter == "inactive":
        statement = statement.where(Client.is_active == False)
        count_statement = count_statement.where(Client.is_active == False)
    # If status_filter is None or "all", don't add any filter
    
    # Apply sorting
    if sort_by == "created_at":
        if sort_order == "asc":
            statement = statement.order_by(Client.created_at.asc())
        else:
            statement = statement.order_by(Client.created_at.desc())
    elif sort_by == "email":
        if sort_order == "asc":
            statement = statement.order_by(Client.email.asc())
        else:
            statement = statement.order_by(Client.email.desc())
    elif sort_by == "full_name":
        if sort_order == "asc":
            statement = statement.order_by(Client.full_name.asc())
        else:
            statement = statement.order_by(Client.full_name.desc())
    
    # Apply pagination
    statement = statement.offset(skip).limit(limit)
    
    # Execute queries
    count = session.exec(count_statement).one()
    clients = session.exec(statement).all()
    
    return ClientsPublic(data=clients, count=count)


@router.post(
    "/admin_clients",
    dependencies=[Depends(get_current_active_superuser)],
    response_model=ClientPublic,
)
def create_client_admin(
    *, session: SessionDep, client_in: ClientCreate
) -> Any:
    """
    Create new client (admin only).
    """
    # Check if client with this email already exists
    client = crud.get_client_by_email(session=session, email=client_in.email)
    if client:
        raise HTTPException(
            status_code=400,
            detail="The client with this email already exists in the system",
        )
    
    # Create client
    client = crud.create_client(
        session=session,
        client_create=client_in,
        is_active=client_in.is_active,  # Use the provided active status
    )
    return client


@router.get(
    "/clients/{client_id}",
    dependencies=[Depends(get_current_active_superuser)],
    response_model=ClientPublic,
)
def read_client_admin(
    client_id: uuid.UUID, session: SessionDep
) -> Any:
    """
    Get a specific client by id (admin only).
    """
    client = session.get(Client, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client not found")
    return client


@router.patch(
    "/admin_clients/{client_id}",
    dependencies=[Depends(get_current_active_superuser)],
    response_model=ClientPublic,
)
def update_client_admin(
    *,
    session: SessionDep,
    client_id: uuid.UUID,
    client_in: ClientUpdate,
) -> Any:
    """
    Update a client (admin only).
    """
    client = session.get(Client, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client not found")
    
    # Check if email is being changed and if it already exists
    if client_in.email and client_in.email != client.email:
        existing_client = crud.get_client_by_email(session=session, email=client_in.email)
        if existing_client:
            raise HTTPException(
                status_code=400, detail="Client with this email already exists"
            )
    
    # Update client
    client_data = client_in.model_dump(exclude_unset=True)
    extra_data = {}
    
    # Handle password update if provided
    if "password" in client_data and client_data["password"]:
        password = client_data["password"]
        hashed_password = get_password_hash(password)
        extra_data["hashed_password"] = hashed_password
        # Remove password from client_data as it's handled separately
        del client_data["password"]
    
    client.sqlmodel_update(client_data, update=extra_data)
    session.add(client)
    session.commit()
    session.refresh(client)
    return client


@router.delete(
    "/admin_clients/{client_id}",
    dependencies=[Depends(get_current_active_superuser)],
    response_model=Message,
)
def delete_client_admin(
    session: SessionDep, client_id: uuid.UUID
) -> Any:
    """
    Delete a client (admin only).
    """
    client = session.get(Client, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client not found")
    
    session.delete(client)
    session.commit()
    return Message(message="Client deleted successfully")
