from typing import Any, Annotated
from app.utils import validate_file_size_type, save_picture, del_picture
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from sqlmodel import func, select

from app.api.deps import CurrentUser, SessionDep
from app.models import (
    Blog,
    BlogCreate,
    BlogsPublic,
    BlogUpdate,
    Message,
)

router = APIRouter(tags=["blogs"], prefix="/blogs")


@router.post(
    "/",
    response_model=Blog,
)
async def create_blog(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    description: str = Form(),
    title: str = Form(),
    image1: Annotated[UploadFile | None, File()] = None,
    image2: Annotated[UploadFile | None, File()] = None,
    image3: Annotated[UploadFile | None, File()] = None,
) -> Any:

    # Create a list to store image URLs
    image_urls = []

    # Process each image if it exists
    for img in [image1, image2, image3]:
        if img is not None and hasattr(img, "file"):  # Check if it's a valid UploadFile
            validate_file_size_type(img)
            image_url = await save_picture(file=img, folder_name="tmp")
            image_urls.append(image_url)
        else:
            # Append None or empty string based on your data model requirements
            image_urls.append(None)

    blog_in = BlogCreate(
        description=description,
        image1=image_urls[0],
        image2=image_urls[1],
        image3=image_urls[2],
        title=title,
    )
    blog = Blog.model_validate(blog_in)
    session.add(blog)
    session.commit()
    session.refresh(blog)
    return blog


@router.put(
    "/{id}",
    response_model=Blog,
)
async def edit_blog(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    id: int,
    description: str = Form(),
    image1: Annotated[UploadFile | None, File()] = None,
    image2: Annotated[UploadFile | None, File()] = None,
    image3: Annotated[UploadFile | None, File()] = None,
    title: str = Form(),
) -> Any:
    blog = session.get(Blog, id)

    # Process each image if it exists
    image_updates = {}
    if image1 is not None:
        validate_file_size_type(image1)
        image_updates["image1"] = await save_picture(file=image1, folder_name="tmp")
        if blog.image1:
            await del_picture(blog.image1)
    if image2 is not None:
        validate_file_size_type(image2)
        image_updates["image2"] = await save_picture(file=image2, folder_name="tmp")
        if blog.image2:
            await del_picture(blog.image2)
    if image3 is not None:
        validate_file_size_type(image3)
        image_updates["image3"] = await save_picture(file=image3, folder_name="tmp")
        if blog.image3:
            await del_picture(blog.image3)

    blog_in = BlogUpdate(
        description=description,
        title=title,
        image1=image_updates.get("image1", blog.image1),
        image2=image_updates.get("image2", blog.image2),
        image3=image_updates.get("image3", blog.image3),
    )

    update_dict = blog_in.model_dump(exclude_unset=True)
    blog.sqlmodel_update(update_dict)
    session.add(blog)
    session.commit()
    session.refresh(blog)

    return blog


@router.put(
    "/{id}/images",
    response_model=Blog,
)
async def delete_blog_images(
    *, session: SessionDep, current_user: CurrentUser, id: int, image: str = Form()
) -> Any:
    blog = session.get(Blog, id)

    if not blog:
        raise HTTPException(status_code=404, detail="Blog not found")

    # Check which image field matches the provided image path
    if blog.image1 == image:
        await del_picture(blog.image1)
        blog.image1 = None
    elif blog.image2 == image:
        await del_picture(blog.image2)
        blog.image2 = None
    elif blog.image3 == image:
        await del_picture(blog.image3)
        blog.image3 = None
    else:
        raise HTTPException(status_code=404, detail="Image not found in this blog")

    session.add(blog)
    session.commit()
    session.refresh(blog)

    return blog


@router.get("/", response_model=BlogsPublic)
async def get_blogs(*, session: SessionDep, skip: int = 0, limit: int = 100) -> Any:
    count_statement = select(func.count()).select_from(Blog)
    count = session.exec(count_statement).one()
    statement = select(Blog).offset(skip).limit(limit)
    blog = session.exec(statement).all()

    return BlogsPublic(data=blog, count=count)


@router.get("/{id}", response_model=Blog)
async def get_blog(*, session: SessionDep, id: int) -> Any:
    """
    Get a specific blog by its ID.
    """
    blog = session.get(Blog, id)
    if not blog:
        raise HTTPException(status_code=404, detail="Blog not found")
    return blog


@router.delete("/{id}")
async def delete_blog(
    session: SessionDep, current_user: CurrentUser, id: int
) -> Message:
    """
    Delete a blog.
    """
    blog = session.get(Blog, id)
    if not blog:
        raise HTTPException(status_code=404, detail="Blog not found")
    # Delete all three images if they exist
    if blog.image1:
        await del_picture(blog.image1)
    if blog.image2:
        await del_picture(blog.image2)
    if blog.image3:
        await del_picture(blog.image3)
    session.delete(blog)
    session.commit()
    return Message(message="Blog deleted successfully")
