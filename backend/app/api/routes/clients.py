from datetime import timedel<PERSON>
from typing import Annotated, Any

from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from fastapi import BackgroundTasks
from app import crud
from app.api.deps import SessionDep, CurrentClient
from app.core import security
from app.core.config import settings
from app.models import Message, Token, ClientPublic, ClientRegister, ClientUpdateMe
from app.utils import (
    send_email,
    verify_email_verification_token,
    generate_email_verification_token,
    generate_email_verification,
)

router = APIRouter(prefix="/clients", tags=["clients"])


@router.post("/login")
def client_login_access_token(
    session: SessionDep, form_data: Annotated[OAuth2PasswordRequestForm, Depends()]
) -> Token:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    client = crud.client_authenticate(
        session=session, email=form_data.username, password=form_data.password
    )
    if not client:
        raise HTTPException(status_code=400, detail="電郵或密碼不正確")
    elif not client.is_active:
        raise HTTPException(status_code=400, detail="帳戶未啟用，請查看電郵")
    access_token_expires = timedelta(minutes=settings.CLIENT_ACCESS_TOKEN_EXPIRE_MINUTES)
    return Token(
        access_token=security.create_access_token(
            client.id, expires_delta=access_token_expires
        )
    )


@router.post("/signup", response_model=ClientPublic)
def register_client(
    session: SessionDep, client_in: ClientRegister, background_tasks: BackgroundTasks
) -> Any:
    """
    Create new client with email verification.
    """
    client = crud.get_client_by_email(session=session, email=client_in.email)
    if client:
        raise HTTPException(
            status_code=400,
            detail="The client with this email already exists in the system",
        )

    # Create client with is_active=False until verified
    client_create = ClientRegister.model_validate(client_in)
    client = crud.create_client(
        session=session,
        client_create=client_create,
        is_active=False,  # Set inactive until email verified
    )

    # Generate verification token
    verification_token = generate_email_verification_token(email=client_in.email)

    # Generate and send verification email
    email_data = generate_email_verification(
        email_to=client.email, email=client.email, token=verification_token
    )

    # Send email in background task
    background_tasks.add_task(
        send_email,
        email_to=client.email,
        subject=email_data.subject,
        html_content=email_data.html_content,
    )

    return client


@router.post("/verify-email/", response_model=Message)
def verify_email(token: str, session: SessionDep) -> Message:
    """
    Verify client email
    """
    email = verify_email_verification_token(token=token)
    if not email:
        raise HTTPException(status_code=400, detail="Invalid token")

    client = crud.get_client_by_email(session=session, email=email)
    if not client:
        raise HTTPException(
            status_code=404,
            detail="The client with this email does not exist in the system.",
        )

    # Activate client
    client.is_active = True
    session.add(client)
    session.commit()

    return Message(message="Email successfully verified")


@router.get("/me", response_model=ClientPublic)
def read_client_me(current_client: CurrentClient) -> Any:
    """
    Get current client profile.
    """
    return current_client


@router.patch("/me", response_model=ClientPublic)
def update_client_me(
    *, session: SessionDep, current_client: CurrentClient, client_in: ClientUpdateMe
) -> Any:
    """
    Update current client profile.
    """
    # Check if email is being changed and if it already exists
    if client_in.email and client_in.email != current_client.email:
        existing_client = crud.get_client_by_email(session=session, email=client_in.email)
        if existing_client:
            raise HTTPException(
                status_code=400, detail="Client with this email already exists"
            )

    # Update client
    current_client = crud.update_client(
        session=session, db_client=current_client, client_in=client_in
    )
    return current_client
