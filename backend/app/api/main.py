from fastapi import APIRouter

from app.api.routes import items, login, private, users, utils, clients, introduction, ma_course, blogs, aboutUs, setting, admin, questionnaires
from app.core.config import settings

api_router = APIRouter()
api_router.include_router(login.router)
api_router.include_router(users.router)
api_router.include_router(utils.router)
api_router.include_router(items.router)
api_router.include_router(clients.router)
api_router.include_router(admin.router)
api_router.include_router(introduction.router)
api_router.include_router(ma_course.router)
api_router.include_router(blogs.router)
api_router.include_router(aboutUs.router)
api_router.include_router(setting.router)
api_router.include_router(questionnaires.router, prefix="/questionnaires", tags=["questionnaires"])


if settings.ENVIRONMENT == "local":
    api_router.include_router(private.router)
